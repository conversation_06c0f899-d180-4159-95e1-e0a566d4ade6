== AccessTokenId ==
Id

== AccessTokenDescription ==
Description

== AccessTokenOwner ==
Owner

== AccessTokenCreatedAt ==
Created at

== AccessTokenExpiresAt ==
Expires at

== AccessTokenLastUsedAt ==
Last used at

== WrongRevFormat3D ==
The revision format specified is not valid

== AcceptResults ==
Do you want to accept the results? (Y/N):

== ActivatedUsers ==
Total active users:     {0}

== ActiveKey ==
ACTIVE

== Add ==
controls whether files and directories can be added to the repository

== AddAndMove ==
Add/Move conflict

== AddMoveConflictAction1 ==
Keep both changes, renaming the destination to

== AddMoveConflictAction2 ==
Keep source changes (preserve the add and discard the move)

== AddMoveConflictAction3 ==
Keep destination changes (preserve the move and discard the add)

== Added ==
Added

== AddedMovedConflictDestinationOperation ==
Moved from {0} to {1}

== AddedMovedConflictExplanation ==
An item has been added on the source and other item has been moved on the destination to the same location.

== AddedMovedConflictSourceOperation ==
Added {0}

== AddedOldNameConflict ==
An item was added with a name which is already in use.

== AddedSameNameConflict ==
Two items were added with the same name

== AdminReadonlyEntered ==
The server entered in read-only mode.

== AdminReadonlyEntering ==
The server is entering in read-only mode. Please wait.

== AdminReadonlyLeaving ==
The server is leaving the read-only mode. Please wait.

== AdminReadonlyLeft ==
The server left the read-only mode.

== Advancedquery ==
controls the advanced query execution

== All ==
All

== AllUsersKey ==
All_users

== AllowedKey ==
Allowed

== AlsoThreeOption ==
You can also: 3. Rename dst to a new name

== AncestorContributor ==
Ancestor changeset: {0} (Branch {1})

== AppliedRename ==
The local rename {0} -> {1} will be applied.

== Applyattr ==
allows the user to apply attributes

== Applylabel ==
allows the user to apply labels

== Archived ==
Archived

== AskDeleteWorkspace ==
No workspace specified. Do you want to delete the current workspace? (Y/N):

== AskDeleteDynamicWorkspace ==
The workspace is dynamic. You will LOSE all of the private items in the workspace if you delete it. Do you want to continue? (Y/N):

== AttValueField ==
Attribute value

== Available ==
Available

== AvailableUsers ==
Total available users:  {0}

== BinFileMetadataChangeset ==
Changeset: {0}

== BinFileMetadataCreated ==
Created by: {0}

== BinFileMetadataModified ==
Date modified: {0}

== BinFileMetadataSize ==
Size: {0}

== BisyncAutoLinkedCommentFormat ==
Changeset {0} and {1} have been automatically linked

== BisyncBranchAttributeError ==
The branch attribute is not correctly formed

== BisyncBranchBothChanges ==
The branch '{0}' has new local and foreign changes {1}

== BisyncBranchExcluded ==
- {0}: Excluded

== BisyncBranchForeignChanges ==
- {0}: Foreign changes {1}

== BisyncBranchLocalChanges ==
- {0}: Local changes {1}

== BisyncBranchMappedNotFound ==
The branch '{0}' mapped to the reference '{1}' was not found.

== BisyncBranchUpToDate ==
- {0}: Up to date {1}

== BisyncBranches ==
Branches

== BisyncBranchesInSync ==
Branches are in sync. There are no changesets to push or pull.

== BisyncChangesetNotTranslated ==
The changeset {0} could not be translated

== BisyncChangesetsInvolved ==
Changesets involved

== BisyncChangesetsNotSorted ==
The changesets to push could not be correctly sorted

== BisyncCheckCommit ==
Check commit

== BisyncCommitCheckFailed ==
Commit check failed

== BisyncCommitDiscarded ==
The commit '{0}' was already imported.

== BisyncCommitNotFound ==
The referenced commit {0} was not found in Plastic

== BisyncCommitedForeignChanges ==
Committed foreign changeset {0} (plastic cset:{1})

== BisyncCommitsCannotBeProcessed ==
There are commits that cannot be processed

== BisyncComplete ==
Synchronization completed

== BisyncCompressing ==
Compressing objects

== BisyncConflictBranches ==
Branches with conflicts

== BisyncConnectException ==
An error occurred while connecting to Git: {0}

== BisyncCsetNotFoundWithGuid ==
Can't find last synced cset with guid {0}

== BisyncCtrlCDetected ==
Ctrl+C pressed. Please wait until the current changeset is synced to prevent data mismatch. After that, the operation will be gracefully stopped.

== BisyncDownloading ==
Downloading

== BisyncDownloadingMappings ==
Upgrading mappings from server

== BisyncErrorPushingCs ==
Error pushing cs:{0}. {1}\n{2}

== BisyncExportComplete ==

Export complete.

== BisyncExportedToCommit ==
Exported cs:{0} to commit ({1})

== BisyncExporting ==
Exporting

== BisyncForeignTreeNotRetrieved ==
The foreign tree for the rev:{0} could not be retrieved

== BisyncGetfileNotValid ==
The GetFile method by path is not supported by the git puller

== BisyncGettingInfo ==
Receiving references

== BisyncGettingInfoDone ==
Receiving references from the remote server, done

== BisyncInvalidCredentials ==
The credentials introduced for the repository are not valid

== BisyncInvalidNodeType ==
The node type is not valid

== BisyncInvalidScm ==
Invalid scm specified

== BisyncItemDiscardedWithoutRevid ==
The item '{0}' with hash '{1}' has not a revid mapping, so, it will be discarded.

== BisyncLastCsetForeign ==
Latest cset on foreign SCM: {0}

== BisyncLastEquivalenceFound ==
Last equivalence found {0}

== BisyncLfsException ==
An error occurred while contacting GitHub LFS: {0}. You can disable the Git LFS support from the command line (cm sync) using the '--skipgitlfs' option.

== BisyncNoChangesPull ==
No new revisions to pull from the foreign SCM

== BisyncNoSettingsMultipleStore ==
No sync settings have been specified and multiple settings were stored for the repository {0}. Please specify the git repository to synchronize.

== BisyncNoSettingsNoStore ==
No sync settings have been specified and no settings were stored for the branch/repo {0}

== BisyncNothingToCommit ==
Nothing to commit!

== BisyncObjectCount ==
Object count

== BisyncPackaging ==
Packaging...

== BisyncPendingChanges ==
There are changes to push and pull.

== BisyncPendingChangesExplanation ==
Will pull the remote changes then you'll have to merge them and push the changes back.

== BisyncProcessingObjects ==
Processing objects:

== BisyncPullComplete ==
Pull complete.

== BisyncPullCsetList ==
Counting changesets to pull...

== BisyncPullingChangesets ==
Importing

== BisyncPushCsetList ==
There are changes to push.

== BisyncPushErrors ==
Failed to push some refs:

== BisyncReferenceWithoutSha ==
References without sha cannot be processed

== BisyncRemoteDeletedSkip ==
The branch '{0}' has been deleted in remote repository and will be skipped.

== BisyncRemoteError ==

      Remote error: {0}


== BisyncRemoteMappingSkip ==
The branch '{0}' came from a remote tag mapping and it will be skipped.

== BisyncResults ==
Results:

== BisyncRevisionDiscardedNotRetrieved ==
The mapped revision {0} with sha '{1} could not be retrieved, so, it will be discarded

== BisyncSettingsNotMatch ==
The settings you've introduced don't match with the stored ones for branch/repo {0}. Stored are: {1}. Mappings are stored here: {2}. Advanced users only: it is possible to delete these mapping and rerun the operation.

== BisyncShaNotFound ==
The sha for the revid:{0} was not found

== BisyncShaNotValid ==
The sha to process is not valid

== BisyncSyncStatusSaved ==
Sync status saved: plastic cset {0} -> {1} commit {2}

== BisyncTagCannotBeApplied ==
The tag '{0}' for the revision '{1}' cannot be applied.

== BisyncTreeNotRetrieved ==
The tree for the cs:{0} could not be retrieved

== BisyncTypeWithoutBuilder ==
The git type {0} has no builder

== BisyncUploadComplete ==
Upload complete.

== BisyncUploading ==
Uploading...

== BisyncWarnPushingCs ==
Warn pushing cs:{0}. {1}\n{2}

== BisyncWrongBranchName ==
Wrong branch name

== BisyncWrongFileFormat ==
The file '{0}' is corrupt. The format of the line '{1}' is wrong.

== Branch ==
Branch

== BranchHistoryEntryBase ==
Base changeset (interval merge only): {0}

== BranchHistoryEntryDate ==
Date: {0}

== BranchHistoryEntryDestination ==
Destination changeset: {0}

== BranchHistoryEntryMergeType ==
Merge type: {0}

== BranchHistoryEntryOwner ==
Owner: {0}

== BranchHistoryEntrySource ==
Source changeset: {0}

== BranchHistoryEntryType ==
Type: {0}

== BringChangesStgApplyingChanges ==
Applying changes...

== BringChangesStgShelvingChanges ==
Shelving changes...

== BringChangesStgUndoingChanges ==
Undoing workspace changes...

== BringChangesStgCalculatingMergeConflicts ==
Calculating merge conflicts for shelveset sh:{0}...

== BringChangesStgApplyingShelveset ==
Applying shelveset sh:{0}...

== BringChangesApplyShelvesetQuestion ==
Applying shelveset sh:{0} will cause merge conflicts.
You can continue applying the shelveset, or cancel the operation.
If you choose to continue, you will be assisted in solving the conflicts.
If you choose to cancel, you can manually apply the shelve later on.
What would you like to do?

== BringChangesApplyShelvesetQuestionViewConflictsOption ==
View the conflicts (lists conflicts and asks you this question again)

== BringChangesApplyShelvesetQuestionApplyShelveOption ==
Apply the shelve (starts the merge conflicts resolution)

== BringChangesApplyShelvesetQuestionCancelMessage ==
You can manually apply the shelveset running the following command:
cm shelveset apply sh:{0}

== CalculatingInitialChangeset ==
Calculating initial changeset

== Cancel ==
Cancel

== CannotBeDeletedChanged ==
The item '{0}' cannot be deleted on disk because it has been changed.

== CannotBeDownloadChanged ==
The file '{0}' cannot be updated with the new content because it has been locally modified.

== CannotChangeRepoPartialSwitch ==
The 'partial switch' command cannot change the repository configured in the workspace. Please remove the repository spec from the branch spec.

== CannotCheckinIncomingChangesInProgress ==
Cannot checkin while there is an Incoming Changes operation in progress. Complete it before retrying the checkin.

== CannotCheckinMergeInProgress ==
Checkin operation cannot be started because there is a merge in progress: Finish it before checkin the changes. In progress merge: {0} from cset {1}

== CannotDeleteChangesetWithPendingChanges ==
It is not possible to delete a changeset in the current workspace branch while there are pending changes.

== CannotDownloadRevision ==
Cannot download revision {0} from server: {1}

== CannotMoveChangesetWithPendingChanges ==
It is not possible to move a changeset in the current workspace branch while there are pending changes.

== CannotRestoreDeleteWithChangesInside ==
The item '{0}' cannot be restored because it had changes in it when it was deleted. Please undo the pending changes and repeat the merge without pending changes or with another conflict resolution.

== CannotUnlockItems ==
Cannot unlock the following items since the current user is not the administrator nor the owner of the locks:{0}

== CantExecuteAdvQuery ==
Can't execute query. Your current directory is probably a private directory.

== Change ==
controls whether files and directories can be modified in the repository

== ChangeBoth ==
You must rename both elements

== ChangeDelete ==
Change/Delete conflict

== ChangeDeleteConflictActions11 ==
Keep source changes (preserve the add and discard the delete)

== ChangeDeleteConflictActions12 ==
Keep destination changes (preserve the delete and discard the add)

== ChangeDeleteConflictActions21 ==
Keep source changes (preserve the change and discard the delete)

== ChangeDeleteConflictActions22 ==
Keep destination changes (preserve the delete and discard the change)

== ChangeDeleteConflictDestinationOperation ==
Deleted {0}

== ChangeDeleteConflictExplanation ==
An item has been {0} on the source, and the destination has deleted the item or its parent

== ChangeDeleteConflictSourceOperation ==
{0} {1}

== ChangeDependencies ==
Change: {0}. Dependencies:

== ChangedItemAlreadyDeleted ==
The item won't be downloaded because it is already deleted in the workspace: '{0}'. Please undo or checkin the local change and retry the operation.

== ChangeReviewNotFound ==
The following review change requests (specified in the checkin comment) cannot be found or are already applied in a previous changeset:{0}{1}

== Changecomment ==
allows the user to change comments

== Changed ==
Changed

== ChangelistBuiltIn ==
Built-in

== ChangelistCherryPickSubtractive ==
Subtractive cherry pick

== ChangelistCherryPicking ==
Cherry pick

== ChangelistDefaultComment ==
Default Unity VCS changelist

== ChangelistEmptyDescription ==
<No description given>

== ChangelistHiddenComment ==
Changelist that contains user-defined hidden changes

== ChangelistIntervalCherryPick ==
Interval cherry pick

== ChangelistIntervalCherryPickSubtractive ==
Subtractive interval cherry pick

== ChangelistManagementChanged ==
Changelist '{0}' successfully edited.

== ChangelistManagementCreated ==
Created changelist '{0}'.

== ChangelistManagementDeleted ==
Changelist '{0}' successfully removed.

== ChangelistMerge ==
Merge

== ChangelistMergeComment ==
Files affected by the merge process

== ChangelistMergeName ==
{0} from cs:{1}

== Changeset ==
Changeset

== CheckedOutKey ==
Checked-out

== CheckedOutChangedKey ==
Checked-out (changed)

== CheckedOutUnchangedKey ==
Checked-out (unchanged)

== CheckinParallelMsg ==
Multi-thread checkin

== RemainingMsg ==
remaining

== CheckinParallelUploadNumThreads ==
uploading {0} blocks in parallel

== CheckinProgressUploadingFileData ==
Uploading file {0} ({1}) to the repository

== CheckinStatusCancelling ==
Cancelling checkin operation

== CheckinStatusConfirming ==
Confirming checkin operation

== CheckinStatusFinished ==
Checkin finished

== CheckinStatusGeneratingdata ==
Assembling checkin data

== CheckinStatusRestoringpermissions ==
Restoring file access

== CheckinStatusStarting ==
Checkin operation starting...

== CheckinStatusUploading ==
Uploading file data

== CheckinStatusValidating ==
Validating checkin data

== CheckoutCannotBeSaved ==
Content for item {0} cannot be saved.

== CherryPick ==
Cherry pick

== CherrypickChangeDeleteConflictName ==
Change/Not loaded conflict

== CherrypickChangeDeleteConflictExplanationForAdded ==
An item has been added on the source, but the destination is not loading its parent

== CherrypickChangeDeleteConflictExplanationForChangedFileOnMissingDir ==
A file has been modified on the source, but the destination is not loading one of its parent

== CherrypickChangeDeleteConflictExplanationForChangedMissingFile ==
A file has been modified on the source, but the destination is not loading it

== CherrypickChangeDeleteConflictSrcActionForAddedItemOnMissingDir ==
Keep source changes (load the directory and add the new item)

== CherrypickChangeDeleteConflictDstActionForAdd ==
Keep destination changes (discard the add)

== CherrypickChangeDeleteConflictSrcActionForChangedFileOnMissingDir ==
Keep source changes (load the directory and add the file with the source content)

== CherrypickChangeDeleteConflictSrcActionForChangedMissingFile ==
Keep source changes (add the file with the new content)

== CherrypickChangeDeleteConflictDstActionForChange ==
Keep destination changes (discard the change)

== Chgowner ==
controls the change owner operation

== Chgperm ==
controls the change permission operation

== ChooseResolution ==
Please choose a resolution for this conflict. Which operation do you want to keep?

== Ci ==
controls the checkin operation

== CleanDiffCalcMerges ==
Calculating merges to branch br:{0}@{1}@{2}

== CleanDiffNotifyFinish ==
Skipped differences from merges

== CleanDiffNotifyTotal ==
Skipping differences from {0} changesets (merge destinations)

== CleanDiffProcessCset ==
Skipped differences from cs:{0}@{1}@{2}

== Cloaked ==
Cloaked

== CloneDstRepositoryAlreadyExists ==
Destination repository '{0}' already exists and it's empty.

== CloneDstRepositoryCreated ==
Destination repository '{0}' created.

== CmUndoIncompatibleFlags ==
The flags '--silent' and '--machinereadable' are not compatible.

== CmUncoIncompatibleFlag ==
The '--keepchanges' option is not compatible with dynamic workspaces.

== CmdApiInvalidPort ==
Incorrect port number: {0}

== CmdApiPressEnterToExit ==
Unity VCS client REST API: Press <ENTER> to exit.

== CmdArchiveErrorRestore ==
There was an error restoring archived data. Please remember that you have to be the Unity VCS server administrator to be allowed to restore archived data. {0}

== CmdAskMkworkspaceConfirmdeletedir ==
The directory {0} will be deleted. Are you sure [Y|N]:

== CmdCannotBePerformedInPartialWk ==
The workspace is in Gluon format and needs to be converted to 'standard'. Just run an update to fix it

== CmdCannotBePerformedInStandardWk ==
The workspace is not in Gluon format. Just run an update to fix it. It is currently a 'standard' workspace (maybe you used it from the regular Unity VCS GUI). Run an update from Gluon to fix it (or use cm partial)

== CmdErrorAclNoSuchUserOrGroup ==
No such user or group

== CmdErrorGetfileCantWriteOutput ==
Error: Access to the path {0} is denied because the file is read-only

== CmdErrorGetfileRevnotfound ==
The specified revision was not found {0}

== CmdErrorIncorrecBrspec ==
Incorrect branch spec

== CmdErrorIncorrecWkspec ==
Incorrect workspace specification

== CmdErrorIncorrectCodeReviewId ==
Incorrect code review ID: {0}

== CmdErrorLabelIncorrectrevspec ==
Incorrect revision spec: {0}

== CmdErrorLabelItemNotFound ==
Could not apply label {0}. The item is private.

== CmdErrorLabelMarkerspecnotfound ==
The specified label spec can't be found. {0}

== CmdErrorListrepRepserverunknown ==
Unknown repository server

== CmdErrorMergeWithModifiedItems ==

    You have changes pending to be checked in in your workspace.
    It is recommended that you checkin prior to merge to avoid possible issues
    undoing merges.

    If you are familiar with merge, you can disable this behaviour adding the
    following key to your 'client.conf' file:

        <MergeWithPendingChanges>yes</MergeWithPendingChanges>

    Note: we changed this behavior to avoid issues to new users, although it is
    not dangerous to enable it if you are familiar with how merge works.


== CmdErrorNoSuchBranch ==
Branch {0} doesn't exist in repository {1}.

== CmdErrorNoSuchTypeTrigger ==
Type {0} is not valid. Remember that its format is subtype-type.

== CmdErrorNoSuchUser ==
The user {0} does not exist

== CmdErrorNoSuchUserDuCommandHint ==
Maybe you have to use the flag "--nosolveuser" to deactivate a user that doesn't exist in the authentication system anymore.

== CmdErrorNoWkFoundFor ==
Can't find a workspace for {0}

== CmdErrorNotLocateItemhandler ==
Could not locate a valid ItemHandler.

== CmdErrorRevertRevnotfound ==
The specified revision to revert to was not found: {0}

== CmdErrorUndocheckoutCantfindrev ==
Specified revision can't be found {0}

== CmdErrorUndocheckoutIdNotCheckedout ==
Specified revision is not checked out {0}

== CmdErrorUndocheckoutIncorrectspec ==
Invalid checkout spec: {0}

== CmdErrorUpdateProcessingItem ==
Error updating {0}. {1}

== CmdErrorWkNotCreatedOnServer ==
Workspace couldn't be created

== CmdMandatoryParameter ==
{0}: The needed parameter {1} is missing

== CmdMessageAddIgnoredError ==
Item {0} cannot be added. Error: {1}

== CmdMessageAddItemadded ==
Item {0} was correctly added

== CmdMessageCiIgnoredError ==
Item {0} cannot be checked in. Error: {1}

== CmdMessageItemcheckout ==
Item {0} was correctly checked out

== CmdMessageManipulateselectorNoselector ==
No selector specified.

== CmdMessageMkworkspaceCorrectlycreated ==
Workspace {0} has been correctly created

== CmdMessageMkworkspaceDynamicWaitToMount ==
Waiting while plasticfs mounts your new workspace (it may take a few more seconds)...

== CmdMessageMvworkspaceDynamicWaitToMount ==
Waiting while plasticfs mounts the workspace in its new location (it may take a few more seconds)...

== CmdMessageMkworkspaceDynamicMounted ==
The new dynamic workspace is ready to use!

== CmdMessageMvworkspaceDynamicMounted ==
The dynamic workspace has been successfully moved!

== CmdMessageMkworkspaceDynamicRequiredParams ==
When creating a dynamic workspace, it is mandatory to specify both the '--dynamic' and '--tree=[tree]' parameters. Check 'cm workspace create --help' for further information.

== CmdMessageNoworkspacesfound ==
There are no workspaces in this machine.

== CmdMessageNoworkspacesfoundInPath ==
No workspaces have been found in path {0}.

== CmdMessageProceedAdd ==
The selected items are about to be added. Please wait ...

== CmdMessageProceedCheckdb ==
Check database integrity may take some time. Please wait...

== CmdMessageProceedCheckin ==
The selected items are about to be checked in. Please wait ...

== CmdMessageProceedShelveset ==
The selected items are about to be shelved. Please wait ...

== CmdMessageProceedCheckout ==
The selected items are about to be checked out. Please wait ...

== CmdMessageRemoveItem ==
Item {0} has been removed.

== CmdMessageShowselectorNoselector ==
No selector is available in this workspace

== CmdMessageWorkspacedeleted ==
The workspace {0} has been deleted.

== CmdMktriggerPosition ==
Trigger created on position {0}.

== CmdMoved ==
{0} has been moved to {1}

== CmdMsgCopiedFromRep ==
Downloaded {0} from {1}

== CmdMsgCreateDir ==
Created directory {0}

== CmdMsgFileAlreadyExistsInWk ==
The file {0} already exists in the workspace

== CmdMsgFileChangedInWk ==
The file {0} has been changed in the workspace. Won't overwrite.

== CmdMsgFileChangedInWkToRm ==
The file {0} has been changed in the workspace. Won't remove.

== CmdMsgFileDateNotChangedInWk ==
The date of file {0} was not modified in the workspace

== CmdMsgItemCantLabelNoLabelOnRep ==
The repository on which revision {0} is located does not contain that label

== CmdMsgLabelCantLabelOtherRep ==
The revision {0} is not in the same repository as the selected label

== CmdMsgLabelCorrectlyLabeled ==
Changeset {0} correctly labeled.

== CmdMsgLsNotloaded ==
Not loaded

== CmdMsgMergeCannotmergecloaked ==
Element {0} won't be merged because it is cloaked.

== CmdMsgMergeCannotmergedir ==
Element {0} discarded to merge, because during the merge process that element has been deleted.

== CmdMsgMergeCannotmergefile ==
Element {0} discarded to merge, because during the merge process that element has been deleted.

== CmdMsgMergeDone ==
Merge done

== CmdMsgMergeGoingtocopymerge ==
Copy-merging {0}

== CmdMsgMergeGoingtomerge ==
Merging {0}

== CmdMsgMergeInvalidinterval ==
The selected changesets interval is not valid

== CmdMsgMergeMergingmoveddir ==
The directory {0} has been moved to {1} and is being merged

== CmdMsgMergeMergingmovedfile ==
The file {0} has been moved to {1} and is going to be merged

== CmdMsgMergeNomergesdetected ==
No merges detected

== CmdMsgMergeNotconnected ==
Merge source and destination must be connected in order to perform a subtractive merge

== CmdMsgNocheckoutsfound ==
No checkouts have been found

== CmdMsgNopathfound ==
Cannot resolve path

== CmdMsgOwner ==
{0,-25} {1} {2}

== CmdMsgRenaming ==
Renaming {0} to {1}

== CmdMsgUpdateStoringCheckout ==
Storing checked out data for {0}

== CmdMsgUpdateWontOverwriteCo ==
Update won't overwrite a checked out file. {0}

== CmdMsgWkinfo ==
Selector for workspace {0}:

== CmdMsgWontOverwriteCheckout ==
Won't overwrite a checked out file. {0}

== CmdNoLicensedUsers ==
There are no active users. Users get activated with the first operation they perform on the system.

== CmdPatchRequiresDiff ==
Diff not found, please download it from:
http://gnuwin32.sourceforge.net/packages/diffutils.htm

Once installed, please add it to the PATH environment variable or use the
--tool parameter to specify the location of diff.exe.

== CmdPatchRequiresPatch ==
Patch not found, please download it from:
http://gnuwin32.sourceforge.net/packages/patch.htm.

Once installed, please add it to the PATH environment variable or use the
--tool parameter to specify the location of patch.exe.

== CmdProfileAuthenticationModeError ==
Could not retrieve the authentication mode from '{0}'

== CmdProfileCreateSuccess ==
Profile '{0}' correctly created

== CmdProfileEnterServerAddress ==
Enter server address

== CmdProfileEnterServerAddressWithServerSuggestion ==
Enter server address [{0}]

== CmdProfileInvalidServerAddress ==
'{0}' is not a valid server address

== CmdProfileInvalidWorkingMode ==
Invalid working mode '{0}'

== CmdProfileNoProfiles ==
There are no defined connection profiles

== CmdProfileRemoveByIndexSuccess ==
Profile '{0}' correctly deleted

== CmdProfileRemoveByIndexFailure ==
There is no profile with index {0}

== CmdProfileRemoveByNameSuccess ==
Profile '{0}' correctly deleted

== CmdProfileRemoveByNameFailure ==
There is no profile with name '{0}'

== CmdProfileRetry ==
Do you want to retry?

== CmdProfileSpecifyOnlyPasswordOrToken ==
Mst specify either '--password' or '--token', but not both nor none

== CmdProfileUnknownWorkingMode ==
Unknown working mode '{0}'

== CmdRepServerResultCheckdb ==
Repository Server: {0}

== CmdRepoResultCheckdb ==
Repository: {0}

== CmdSetselectorEndwithdot ==
Reading selector config from stdin, end with '.' in a single line

== CmdStatusIncompatibleOptionsShortAndXml ==
The --short and --xml options cannot be used together since --short does not load detailed status information.

== CmdUnchangeDone ==
{0} Undo change done.

== CmdUncheckedOut ==
{0} unchecked out correctly

== CmdUnexpectedOption ==
{0}: Unexpected option {1}

== CmdInvalidValueForOption ==
Invalid value for option: {0}={1}

== CmdServerCannotBeInferred ==
Could not infer which server must be used to run this command.
Please provide one explicitly using the --server=<server> option.

== CmdRepositoryCannotBeInferred ==
Could not infer which repository must be used to run this command.
Please provide one explicitly using the --repository=<repository> option.

== CmdUpdated ==
{0} updated correctly

== CmdUpdateReportItemMessage ==
{0} updating '{1}': {2}

== CmdUpdateReportWarningSeverity ==
Warning

== CmdUpdateReportErrorSeverity ==
Error

== CommandFinishedSuccesfully ==
Command finished successfully

== Comment ==
Comment

== CommentExplanation ==


# Please enter a checkin comment for your changes.
# A checkin comment has a maximum of 1000 characters.
# Lines starting with "#" will be ignored.


== Committing ==
Checking in

== Configlocks ==
allows adding new lock rules

== ConfirmNewPassword ==
Confirm password:

== ConnectionFail ==
Test connection encountered a problem trying to connect to Unity VCS server

== ConnectionOk ==
Test connection executed successfully

== ContinueSolving ==
{0} conflicts left, do you want to continue solving conflicts? (Y/N):

== ContinueWithPendingChangesQuestion ==
There are changed files in your workspace. You can cancel the switch operation in order to check the changed files or you can continue with the operation. If you continue with the switch operation, your changed files won't be lost, and will still appear as changed after the switch operation. Do you want to continue with the switch operation? (y/n)

== Controlled ==
Controlled

== Copied ==
Copied (new)

== CopiedCannotBeApplied ==
The item '{0}' can't be copied because of the error: '{1}'.

== CopyMergeIsRealMerge ==
The item {0} is currently loaded in the workspace, so it needs to be merged instead of copy-merged.

== CopyMergeNeeded ==
Copy merge needed on item {0} from {1}

== CopyMergePromoted ==
The copy-merge for the item {0} has been promoted to merge

== CopyingMergeFiles ==
Copying merge files ...

== CreatedChangesetNumber ==
Created changeset {0}

== CreatedOn ==
Created

== CreatedShelveNumber ==
Created shelve {0}

== CsetNumber ==
Changeset number

== CurrentOutputDirectory ==
Current output directory is

== CycleMove ==
Cycle move conflict

== CycleMoveConflictActions1 ==
Keep the move done on source (discarding the move on destination)

== CycleMoveConflictActions2 ==
Keep the move done on destination (discarding the move on source)

== CycleMoveConflictDestinationOperation ==
Moved from {0} to {1}

== CycleMoveConflictExplanation ==
Two items have been moved on source and destination and collide because they create a cycle.

== CycleMoveConflictSourceOperation ==
Moved from {0} to {1}

== DataStatus ==
Data status

== DataWritten ==
Data written

== Default ==
Default

== DefaultFormatDiffmetrics ==
changed: {0}  added: {1}  deleted: {2}

== DeleteAndChange ==
Delete/Change conflict

== DeleteAndMove ==
Delete/Move conflict

== DeleteCannotBeApplied ==
The delete operation ({0}) cannot be applied because the item cannot be found in the workspace.

== DeleteChangeConflictActions11 ==
Keep source changes (preserve the delete and discard the add)

== DeleteChangeConflictActions12 ==
Keep destination changes (preserve the add and discard the delete)

== DeleteChangeConflictActions21 ==
Keep source changes (preserve the delete and discard the change on destination)

== DeleteChangeConflictActions22 ==
Keep destination changes (preserve the change and discard the delete)

== DeleteChangeConflictDestinationOperation ==
{0} {1}

== DeleteChangeConflictExplanation ==
An item has been deleted on the source and the destination has {0} it.

== DeleteChangeConflictSourceOperation ==
Deleted {0}

== DeleteChangesetDiscardAllChangesNotInteractive ==
Someone deleted the changeset you are working on.
It means your checkouts and local changes are not valid anymore.

This is because there is no way to find the actual changes since the base changeset is now gone.

You need to undo all your changes and update your workspace to a valid configuration.
This can be achieved if you run the command in an interactive mode (i.e. without stdin redirection).

== DeleteChangesetDiscardAllChangesQuestion ==
Someone deleted the changeset you are working on.
It means your checkouts and local changes are not valid anymore.

This is because there is no way to find the actual changes since the base changeset is now gone.

Do you want to undo all your changes and update your workspace to a valid configuration?(y/n)

== DeleteDelete ==
Delete delete warning

== DeleteDeleteConflictDestinationOperation ==
Deleted {0}

== DeleteDeleteConflictSourceOperation ==
Deleted {0}

== DeleteDeleteWarningMessage ==
This item has been discarded because it has been deleted on both source and destination

== DeleteItemAlreadyChanged ==
The item won't be deleted because it is already changed (checked-out, moved or reverted) in the workspace: '{0}'. Please undo or checkin the local change and retry the operation.

== DeleteMoveConflictActions1 ==
Keep source changes (preserve the delete and discard the move)

== DeleteMoveConflictActions2 ==
Keep destination changes (preserve the move and discard the delete)

== DeleteMoveConflictDestinationOperation ==
Moved from {0} to {1}

== DeleteMoveConflictExplanation ==
An item has been deleted on the source and it was moved on the destination.

== DeleteMoveConflictSourceOperation ==
Deleted {0}

== DeletePrivateDeletesSummary ==
Deleted {0} individual files and {1} directory trees.

== DeletePrivateDirectoryFailed ==
Could not delete controlled directory: {0}

== DeletePrivateDryrun ==
Note: this was a dry run. No files were deleted.

== DeletePrivateFailuresSummary ==
Could not delete {0} individual files and {1} directory trees.

== DeletePrivateFileFailed ==
Could not delete controlled file: {0}

== DeletePrivateSkipControlledDir ==
Skipped controlled directory: {0}

== DeletePrivateSkipControlledFile ==
Skipped controlled file: {0}

== DeletePrivateSkipIgnoredDir ==
Skipped ignored directory: {0}

== DeletePrivateSkipIgnoredFile ==
Skipped ignored file: {0}

== DeletePrivateSkipMissingFileOrDirectory ==
Skipped missing file or directory: {0}

== Deleted ==
Deleted

== DeletedPrivateDirectory ==
Deleted directory: {0}

== DeletedPrivateFile ==
Deleted file:      {0}

== DeniedKey ==
Denied

== DependenciesApplyLocalChanges ==
apply local changes

== DependenciesCheckin ==
checkin

== DependenciesDescription ==
Some items selected to {0} depend on others that need to be included in the operation as well. All the dependent items paths should be included on the given paths to {0}. The option "--dependencies" can also be used to include all the dependent items automatically.

== DependenciesShelve ==
shelve

== DependenciesUndoChanges ==
undo changes

== Destination ==
Destination

== DestinationRevisionNotFound ==
The destination revision was not found to promote a merge

== DiffNotDownloadedRevision ==
Revision {0} could not be downloaded from the server. Please try again selecting a different file and then coming back to this one.

== DifferentRepositorySpecs ==
Can't specify objects from different repositories

== DirConflictCannotBeApplied ==
The conflict {0} with source:'{1}' and destination:'{2}' can't be applied because of the error: '{3}'.

== DiscardedAddedWarningMessage ==
This item has been discarded because the source added it, but item is already loaded in the destination, so it is not necessary to add it

== DiscardedChangedWarningMessage ==
This item has been discarded because the content of the revision has not changed from source to destination, or because source and destination load the same revision

== DiscardedDeleteWarningMessage ==
This item has been discarded because the source deleted it, and it is not loaded in the destination, so it cannot be deleted

== DiscardedFsProtectionFormat ==
Discarded FS protection for item {0} from {1} because {2}

== DiscardedFsProtectionWarningMessage ==
This item has been discarded because its filesystem protections have been changed to the same value in source and destination, the source has changed the fs prots and has been deleted on the dst, or the source has deleted the item and the destination has changed the fsprot

== DiscardedMergeConnectedrevision ==
the source revision is already connected with the destination revision.

== DiscardedMergeFormat ==
Discarded merge on item {0} from {1} because {2}

== DiscardedMergeNotconnectrevision ==
the source revision isn't connected with the destination revision.

== DiscardedMergeNotloaded ==
the revision can't be loaded anymore (element removed).

== DiscardedMergeSamerevision ==
the source revision is the same one as the loaded revision in the workspace.

== DiscardedMovedWarningMessage ==
This item has been discarded because the item has been moved to the same location in source and destination, or the item is already loaded on destination

== DiscardedSubtractiveFormat ==
Discarded subtractive merge on item {0} from {1} because {2}

== DivergentMove ==
Divergent move conflict

== DivergentMoveConflictActions1 ==
Keep the move done on source (discarding the move on destination)

== DivergentMoveConflictActions2 ==
Keep the move done on destination (discarding the move on source)

== DivergentMoveConflictDestinationOperation ==
Moved from {0} to {1}

== DivergentMoveConflictExplanation ==
An item was moved on source and destination to two different locations.

== DivergentMoveConflictSourceOperation ==
Moved from {0} to {1}

== DownloadMissingFileNotFoundOnSource ==
Can't download '{0}' (revid:{1}). It was probably replicated with --nodata, but its data can't be found in replication source {2}@{3}.

== DownloadMissingFileReplicationSourceGuidsResolutionMethodNotAvailable ==
Can't download '{0}' (revid:{1}). It was probably replicated with --nodata, but its replication source {2}@{3} is not upgraded so a required API is not available. Please upgrade the server {3}.

== DownloadMissingFileReplicationSourceNotAvailable ==
Can't download '{0}' (revid:{1}). It was probably replicated with --nodata, but its replication source {2}@{3} is not available.

== DownloadMissingFileWithoutReplicationSource ==
Can't download '{0}' (revid:{1}). It was probably replicated with --nodata, but it is not available in the repository {2}@{3}.

== DstContributor ==
Destination changeset: {0} (Branch {1})

== ElementNewName ==
Element with new name {0}

== ElementOldNewName ==
Element with old name {0} and new name {1}

== EnterNewDestinationName ==
Please enter a new name for the destination:

== Error ==
Error

== ErrorCheckingIn ==
Error checking in {0}. {1}

== ErrorCloneDstRepNotEmpty ==
The destination repository '{0}' is not empty, and thus the clone operation cannot continue.

== ErrorCloneDstRepNotFound ==
Could not determine destination repository for the clone operation.

== ErrorClonePackageNotValid ==
The option --package cannot be used if the destination is specified.

== ErrorCloneSourceRepoNotSpecified ==
The source repository has not been specified.

== ErrorCloneSrcAndDstEquals ==
Source and destination repositories are the same ('{0}').

== ErrorCloneSrcRepNotFound ==
Could not determine source repository for the clone operation from '{0}'.

== ErrorExecutingQuery ==
Error executing query:

== ErrorImportingCommit ==
The changeset '{0}' could not be imported. Error: {1} Please contact with the support team.

== ErrorLaunchingEditor ==
Can't run editor: {0}

== ErrorPullNodataNotValid ==
The option --nodata cannot be used replicating packages.

== ErrorPushHydrateNotValid ==
The option hydrate cannot be used during a push.

== ErrorPushNodataNotValid ==
The option --nodata cannot be used during a push.

== ErrorReplicateDestinationRepoNotSpecified ==
The destination repository has not been specified

== ErrorReplicateHydrateSourceNotFound ==
Hydrate source repository cannot be obtained automatically for '{0}'. Please specify the source repository manually.

== ErrorReplicateNodataNotValid ==
The option --nodata cannot be used replicating packages nor with the --push option.

== ErrorReplicatePackageNotSpecified ==
Neither a replication package nor a destination repository have been specified

== ErrorReplicateSourceBranchNotSpecified ==
The source branch has not been specified

== ErrorRepositoriesDontMatch ==
The repositories you specified don't match

== EvilTwin ==
Evil twin conflict

== EvilTwinConflictActions1 ==
Keep both changes, renaming the destination to

== EvilTwinConflictActions2 ==
Keep the item added on source (discarding the add on destination)

== EvilTwinConflictActions3 ==
Keep the item added on destination (discarding the add on source)

== EvilTwinConflictDestinationOperation ==
Added {0}

== EvilTwinConflictExplanation ==
An item has been added on the source and on the destination with the same name, and they are different items.

== EvilTwinConflictSourceOperation ==
Added {0}

== Excluded ==
{0} has been excluded.

== ExclusiveCheckoutDetail ==
{0} checked out by user {1} at {2}@{3}

== ExclusiveCheckoutDetailShort ==
{0} checked out by user {1}

== ExpirationDate ==
Expiration date:        {0}

== False ==
No

== FastUpdCannotBePerformedInPartialWk ==
The 'fast update' option is not allowed in Gluon workspaces. Please disable the option and retry the operation again.

== FastUpdChanges ==
The following changes have been applied:

== FastUpdDownloadProgress ==
Downloaded {0} of {1} ({2:0.##%})

== FastUpdProcessProgress ==
Processed {0} of {1} ({2:0.##%})

== FastUpdStageApplyingChanges ==
Applying changes...

== FastUpdStageCalculatingChanges ==
Calculating changes...

== FastUpdStageCancelling ==
Cancelling fast update operation...

== FastUpdStageCompilingInfo ==
Assembling fast update data...

== FastUpdStageFinished ==
Fast update finished

== FastUpdWarnings ==
The following changes couldn't be applied on disk:

== Fatal ==
Fatal

== FemaleNone ==
None

== FetchingAcls ==
Fetching ACLs

== FetchingAttributes ==
Fetching attributes

== FetchingBranches ==
Fetching branches

== FetchingChangesets ==
Fetching changesets

== FetchingChildren ==
Fetching trees

== FetchingFinished ==
Transferring metadata

== FetchingItems ==
Fetching items

== FetchingLabels ==
Fetching labels

== FetchingLinks ==
Fetching links

== FetchingMetadata ==
Fetching metadata

== FetchingMoveRealizations ==
Fetching move operations

== FetchingReferences ==
Fetching references

== FetchingReviews ==
Fetching code reviews

== FetchingRevisions ==
Fetching trees

== FetchingSeids ==
Fetching SEIDs

== File ==
file

== FileModifiedSource ==
The file {0} was modified on source and will replace the destination version

== FileTypesFileHeader ==

      # Unity VCS extensions file. Syntax: <expression>:<type>.
      # Valid expressions are: '.cpp', 'myfile.cpp' ... (Metacharacters are allowed: '*', '?')
      # Allowed types are: 'txt' or 'bin'.
      # Examples:
      #     .cpp:txt
      #     .jpg:bin


== Files ==
files

== FinishedNotOk ==
Canceled

== FinishedOk ==
Finished OK

== FinishedStatus ==
Replica interrupted {0}

== FinishedWithErrors ==
Finished with errors

== First ==
1st

== From ==
from

== FsProtectionCannotBeApplied ==
The item '{0}' FS protections can't be applied because of the error: '{1}'.

== GameuiCheckinChangedFileConflictAction ==
Please undo your local changes, download the latest version and reapply your local changes.

== GetfileRevdatanotfound ==
No data found for the given spec

== Group ==
Group

== GroupKey ==
Group

== Hash ==
Hash code

== HiddenChanged ==
Hidden changed

== Ignored ==
Ignored

== InactiveKey ==
INACTIVE (Not licensed)

== Info ==
Information

== InheritedFromKey ==
InheritedFrom

== IntervalMerge ==
Interval merge

== IntroducingData ==
Introducing data

== IssueTrackerCheckinFailed ==
Unable to log checkin data into issue tracker: {0}

== IssueTrackerNotSupported ==
The specified issue tracker '{0}' is not supported.

== ItemAddedSource ==
The item {0} has been added on source and will be added as result of the merge

== ItemAlreadyChanged ==
The item won't be downloaded because it is already changed (checked-out, locally changed, deleted, moved or reverted) in the workspace: '{0}'. Please undo or checkin the local change and retry the operation.

== ItemAlreadyLoaded ==
The item won't be downloaded because it is already loaded in the workspace with a different version: '{0}'. Please unload the item and retry the operation.

== ItemDeletedSource ==
The item {0} has been deleted on source and will be deleted as result of the merge

== ItemFsprotToApply ==
The item {0} has been changed the filesystem permissions on source

== ItemId ==
Item ID

== ItemMovedSource ==
The item {0} has been moved to {1} on source and will be moved as result of the merge

== ItemNotFound ==
Can't find the item {0}

== ItemPathAlreadyUsed ==
The item won't be downloaded/moved because there is another item loaded in the same path: '{0}'. Please unload the item and retry the operation.

== ItemPathAlreadyUsedByChange ==
The item won't be downloaded/moved because there is an existing change at '{0}'. Please undo the local change, unload the item (from the configuration view) and retry the operation.

== KeyEditable ==
Editable

== KeyPrivate ==
private

== KeyWorkspaceRevision ==
Workspace Revision

== Label ==
Label

== LicenseEdition ==
Edition:                {0}

== LicenseInformation ==
License information:

== LicenseUsage ==
License usage:

== LicensedTo ==
Licensed to:            {0}

== LicensedUsers ==
Total licensed users:   {0}

== ListFindObjects ==
Available objects and attributes:

== ListPermissionsKey ==
Available permissions:

== ListTriggerTypes ==
Available trigger types:

== LkNameField ==
Link name

== LoadedRevision ==
The revision {0}@{1} has been loaded

== LoadedTwice ==
Item loaded twice conflict

== LoadedTwiceConflictActions1 ==
Keep the add done on source (discarding the add on destination)

== LoadedTwiceConflictActions2 ==
Keep the add done on destination (discarding the add on source)

== LoadedTwiceConflictDestinationOperation ==
Added {0}

== LoadedTwiceConflictExplanation ==
Two items have been added on source and destination and collide because they are the same item.

== LoadedTwiceConflictSourceOperation ==
Added {0}

== LocalAndDownloadedSameFile ==
File {0} won't be downloaded because the local one matches the content of the one to be downloaded. cset:{1}

== LocatedOnBranch ==
Located on branch

== LogAdded ==
Added

== LogChanged ==
Changed

== LogDefaultCsFormat ==
Changeset number: {changesetid}{newline}Branch: {branch}{newline}Owner: {owner}{newline}Date: {date}{newline}Comment: {comment}{newline}Changes:{newline}{items}------------------------------------------------------------

== LogDefaultCsFormatWithoutChanges ==
Changeset number: {changesetid}{newline}Branch: {branch}{newline}Owner: {owner}{newline}Date: {date}{newline}Comment: {comment}{newline}------------------------------------------------------------

== LogDefaultItemsFormat ==
 {shortstatus} {path} {newline}

== LogDeleted ==
Deleted

== LogMoved ==
Moved

== Markers ==
Markers

== Merge ==
Merge

== MergeBothChanges ==
both contributors.

== MergeDstChanges ==
destination contributor.

== MergeExtraInfoBase ==
Base

== MergeExtraInfoCommentsLine ==
Comments: {0}

== MergeExtraInfoContributorLine ==
{0} from {1}{2} created by {3} on {4}

== MergeExtraInfoDestination ==
Destination

== MergeExtraInfoRecursiveMergeLine ==
RECURSIVE MERGE: This is a recursive merge. It means there are multiple ancestors instead of just one.
Unity VCS will solve it automatically merging the multiple common ancestors to create a single one that will be used to merge the source and the destination contributors.

Now you are merging the ancestor {0} with ancestor {1}, and it will generate a virtual ancestor vcs:{2} that will be used later.

== MergeExtraInfoSource ==
Source

== MergeInProgress ==
You've a merge in progress from changeset {0}@{1}. Finish this merge before merging from a different source

== MergeNeeded ==
The file {0} needs to be merged from {1} to {2} base {3}. Changed by {4}

== MergeNoneChanges ==
none.

== MergeProgressRecursive ==
recursive

== MergeProgressString ==
Merging file {0}/{1}

== MergeResultMultipleLinkTargets ==
Merge result has left multiple targets for the symbolic link {0}

== MergeSourceFormat ==
{0} at {1}@{2}

== MergeSrcChanges ==
source contributor.

== MergeToMergeNeeded ==
A new changeset ({4}) has been created at branch {0}@{1}@{2} (mount '{3}') during the merge-to operation. You need to merge again to complete the operation.

== MergeVirtualChangesetComment ==
Virtual changeset created during the recursive merge

== MergedFile ==
Merged file {0} of {1}: {2}

== Mergefrom ==
controls the merge from operation

== MergingFile ==
Merging file {0} of {1}: {2}

== MessageBranchSelector ==
Select a branch to get revisions (/main if none):

== MessageCheckingIn ==
Checking in {0} ...

== MessageCheckingOut ==
Checking out {0} ...

== MessageCoSelector ==
Select a branch where checkouts will go (/main if none):

== MessageContinueWithSelector ==
This is the resulting selector. Make it active? (Y/N):

== MessageDone ==
Done

== MessageFileNotFound ==
The file {0} does not exist.

== MessageFileNotRead ==
The file {0} could not be read.

== MessageLabelSelector ==
Select a label to get revisions from:

== MessageMoreRepositories ==
More Repositories? (Y/N):

== MessageNoRepositories ==
There are no repositories in this server.

== MessageNumItemSelector ==
Item Selector #{0} for Repository Selector #{1}

== MessageNumRepSelector ==
Repository Selector #{0}

== MessageRepositoryName ==
Select a repository name:

== MessageRepositoryServer ==
Select a repository server:

== MessageSelectorNotSet ==
The selector has not been set.

== Mkattr ==
allows the user to create attributes

== Mkchildbranch ==
allows the user to create child branches

== Mklabel ==
allows the user to create labels

== Mkrepository ==
allows the user to create repositories

== Mktoplevelbranch ==
allows the user to create top-level branches

== Mktrigger ==
allows the user to create triggers

== Modified ==
Modified

== Move ==
controls whether files and directories can be moved in the repository

== MoveAddConflictAction1 ==
Keep both changes, renaming the destination to

== MoveAddConflictAction2 ==
Keep source changes (preserve the move and discard the add)

== MoveAddConflictAction3 ==
Keep destination changes (preserve the add and discard the move)

== MoveAndAdd ==
Move/Add conflict

== MoveAndDelete ==
Move/Delete conflict

== MoveCannotBeApplied ==
The move operation ({0} -> {1}) cannot be applied because the item or the destination parent cannot be found in the workspace.

== MoveDeleteConflictActions1 ==
Keep source changes (preserve the move and discard the delete)

== MoveDeleteConflictActions2 ==
Keep destination changes (preserve the delete and discard the move)

== MoveDeleteConflictDestinationOperation ==
Deleted {0}

== MoveDeleteConflictExplanation ==
An item has been moved on the source and the destination has deleted it or its parent.

== MoveDeleteConflictSourceOperation ==
Moved from {0} to {1}

== MoveItemAlreadyChanged ==
The item won't be moved because it is already changed (checked-out, locally changed, deleted, moved or reverted) in the workspace: '{0}'. Please undo or checkin the local change and retry the operation.

== MoveSourceDelete ==
Move out of delete conflict

== MoveSourceDeleteConflictDestinationOperation ==
Deleted {0}

== MoveSourceDeleteConflictSourceOperation ==
Moved from {0} to {1}

== Moved ==
Moved

== MovedAddedConflictDestinationOperation ==
Added {0}

== MovedAddedConflictExplanation ==
An item has been moved on the source and an item with the same name has been added on the destination on the same location.

== MovedAddedConflictSourceOperation ==
Moved from {0} to {1}

== MovedEvilTwin ==
Moved evil twin conflict

== MovedEvilTwinConflictActions1 ==
Keep both changes, renaming the destination to

== MovedEvilTwinConflictActions2 ==
Keep the move done on source (discarding the move on destination)

== MovedEvilTwinConflictActions3 ==
Keep the move done on destination (discarding the move on source)

== MovedEvilTwinConflictDestinationOperation ==
Moved from {0} to {1}

== MovedEvilTwinConflictExplanation ==
Two different items with the same name have been moved to the same location on source and destination.

== MovedEvilTwinConflictSourceOperation ==
Moved from {0} to {1}

== MovedLocally ==
Moved locally

== MsgBinaryFile ==
bin

== MsgDirectory ==
dir

== MsgItemAdded ==
under Unity VCS control and is recently added.

== MsgItemAddedNotOnDisk ==
under Unity VCS control and is recently added but not on disk.

== MsgItemChanged ==
under Unity VCS control and is changed locally.

== MsgItemCheckedin ==
under Unity VCS control and is checked in.

== MsgItemCheckedinNotOnDisk ==
under Unity VCS control and is checked in but not on disk.

== MsgItemCheckedout ==
under Unity VCS control and is checked out.

== MsgItemCheckedoutNotOnDisk ==
under Unity VCS control and is checked out but not on disk.

== MsgItemIgnored ==
ignored.

== MsgItemNotOnDisk ==
not on disk.

== MsgItemPrivate ==
private.

== MsgItemStatus ==
The item {0} is {1}

== MsgLinkFile ==
link

== MsgNotLoaded ==
NotLoaded

== MsgPendingDstMergelink ==
to cs:{0} at {1}@{2}@{3}

== MsgPendingMergelink ==
{0} from cs:{1} at {2}@{3}@{4}

== MsgPrivFile ==
File

== MsgPrivate ==
Private

== MsgTextFile ==
txt

== MsgUnknown ==
Unknown

== MsgWorkspaceWorkingInfo ==

      Repository: {0}
Branch: {1}
      Label: {2}


== Multiple ==
Multiple

== NameAlreadyInDirectory ==
Name {0} is already in the directory

== NameEmptyMultilineNotValid ==
The file that contains the name {0} does not allow neither empty nor multiline text

== NameNotValid ==
The name {0} is not valid anymore

== Never ==
Never

== NewBrNameParamNotBrspec ==
The new name parameter must contain a branch name and not a branch specification

== NewNameFor ==
New name for {0}:

== NewNameInstead ==
New name instead of {0}:

== No ==
No

== NoChangesApplied ==
No changes were applied

== NoChangesInWk ==
There are no changes in the workspace {0}

== NoPathSpecified ==
No path specified.

== NoErrorsDetected ==
No errors detected.

== NoValidAnswers ==
n;no

== NodeChangeAdded ==
Added {0}

== NodeChangeCopied ==
Copied {0}

== NodeChangeDeleted ==
Deleted {0}

== NodeChangeModified ==
Modified {0}

== NodeChangeModifiedAndMoved ==
Modified and moved from {0} to {1}

== NodeChangeMoved ==
Move from {0} to {1}

== NodeChangeReplaced ==
Replaced {0}

== NotMergedFile ==
Not merged file {0} of {1}: {2}

== Of ==
of

== OldRenameRenameNames ==
Old name {0}, rename 1st {1}, rename 2nd {2}

== On ==
on

== OnlyModifiedOnSource ==
Processed {0} of {1} files only modified on source: {2}

== OperationAbandoned ==
Abandoned

== OperationDisabled ==
This operation is disabled in Plastic SCM 5.0

== OperationStartingFetch ==
Starting Fetch

== OperationStartingPush ==
Starting Push

== Owner ==
Owner

== ParentItemDeleted ==
The item won't be downloaded because its parent directory was deleted in the workspace: '{0}'.

== ParentItemDiscarded ==
The item '{0}' won't be downloaded because its parent directory couldn't be downloaded either.

== PasswordCantBeEmpty ==
The password can't be empty. Password not set.

== PasswordCorrectlySet ==
Password correctly set

== PasswordDoesntMatch ==
Passwords doesnt match. Password not set.

== ItemPathOrSpec ==
Item path or spec

== PathInConflictWarningMessage ==
The item '{0}' has been renamed to '{1}' because the path was already in use during the merge operation

== PathNotFound ==
The path {0} was not found in merge conflicts

== PathNotFoundInDiff ==
The path {0} was not found in the set of diffs

== PathNotUnique ==
The path '{0}' is not unique. It could refer to any of...

== PathsFsLocked ==
The operation cannot start because some files or directories are locked by another program. Please close the program and try again.

== PendingMergeLinks ==
Pending merge links

== PerformingSwitch ==
Performing switch operation...

== PermissionKey ==
Permission

== Purge ==
controls the ability to register, query and execute purges

== PurgeNotFound ==
Purge not found: '{0}'.
Please check that it is the right ID, and be sure to have the required permissions.

== PurgeNotReadyToRun ==
The requested purge action is not ready to be executed.

== PurgeIsGettingReady ==
Now calculating revisions to purge... Please wait.

== PurgeCannotBeDeleted ==
This purge cannot be deleted from the registry anymore.

== PurgeIsWaitingToRun ==
The requested purge action is already waiting to run.

== PurgeIsRunning ==
The requested purge action is already running.

== PurgeIsAlreadyBeingDeleted ==
The purge is being deleted from the registry. You cannot execute it nor delete it again.

== PurgeWasAlreadyExecuted ==
The requested purge action was already executed.

== PurgeAuthorIsNotYou ==
You are not the purge author. Only the purge author is allowed to execute or unregister it.

== PurgeRunAreYouSure ==
This operation cannot be undone or canceled. You will permanently lose the revisions of these files.
Tip: if you want to inspect all the affected revisions, do NOT continue and run this command:
  $ cm purge show {0} --verbose --server={1}
Are you sure you want to execute the purge? (y/n)

== PurgeRunSucceeded ==
The purge action is now running.

== PurgeRunFailed ==
The purge action failed to start.

== PurgeDeletionSucceeded ==
The purge action was unregistered.

== PurgeDeletionFailed ==
Failed to unregister the purge action.

== PurgeInvalidId ==
The input purge ID is invalid: {0}
Example of valid ID:           0131098b-2ff3-44a7-bfd3-233c13225103

== Private ==
Private

== ProcessingBranches ==
Processing branches

== ProcessingDirectoryConflicts ==
Processing directory conflicts

== ProcessingDirectoryOperations ==
Processing directory operations

== ProcessingDirectoryOperationsApplyingFsProtections ==
Processing directory operations (applying filesystem protections)

== ProcessingDirectoryOperationsDownloadingRevisions ==
Processing directory operations (downloading revisions)

== ProcessingDirectoryOperationsUpdatingWorkspace ==
Processing directory operations (updating workspace)

== PurgeNotAnExtension ==
'{0}' is not a file extension. Did you mean this?: '.{0}'.

== PurgeBeforeDateInvalidSetInFuture ==
Invalid date '{0}' for file type '{1}': it is set in the future.

== PushingAcls ==
Pushing ACLs

== PushingAttributes ==
Pushing attributes

== PushingBranches ==
Pushing branches

== PushingChangesets ==
Pushing changesets

== PushingChildren ==
Pushing trees

== PushingFinished ==
Finished pushing metadata

== PushingItems ==
Pushing items

== PushingLabels ==
Pushing labels

== PushingLinks ==
Pushing links

== PushingMetadata ==
Pushing metadata

== PushingMoveRealizations ==
Pushing moves

== PushingReferences ==
Pushing references

== PushingRevisions ==
Pushing revisions

== Read ==
controls whether objects can be read

== ReconcilingAcls ==
Reconciling ACLs

== ReconcilingObjects ==
Reconciling objects

== RecurseWkIncompatibleWithWorkspacePath ==
Can't use '--recursewk' flag with a workspace path.

== Removed ==
Removed

== RemovedLocally ==
Removed locally

== Rename ==
controls whether objects can be renamed

== RenamedOldNameConflict ==
An item was renamed with an name still in use.

== RenamedRenamedConflict ==
An item has been renamed in both contributors.

== RenamedSameNameConflict ==
Two items were renamed with the same name

== RenamedToDownloadFile ==
File {0} will be renamed to download a new version. cset:{1}

== RenamesNotValidIntroduce ==
The renames are not valid anymore, you must introduce a new name manually

== Replaced ==
Replaced

== Replicateread ==
controls the replicate read operation

== Replicatewrite ==
controls the replicate write operation

== ReportCmdMsgFileChangedInWk ==
This file has been changed in the workspace, out of Unity VCS control, so data has been preserved. Item is not up-to-date. If you want to update it try to update forced this item

== ReportCmdMsgUpdateStoringCheckout ==
Safety storing checked out data of this item

== Repository ==
Repository

== RepositorySpec ==
Repository spec

== ResolutionConfigured ==
The conflict was resolved with the configured resolution.

== ResolvedCreatedConflict ==
The conflict {0} has been created after the conflict {1} was resolved

== ResolvedRemovedConflict ==
The conflict {0} has been removed after the conflict {1} was resolved

== ReviewStatusReviewed ==
Reviewed

== ReviewStatusReworkRequired ==
Rework required

== ReviewStatusUnderReview ==
Under review

== RevisionCheckedout ==
local

== RevisionHistoryOf ==
REVISION HISTORY OF {0}

== RevisionNotExists ==
The revision doesn't exist

== RevisionNotFound ==
The revision to load was not found

== RevisionNumber ==
Revision number

== RevisionSpec ==
Revision spec

== RevisionType ==
Revision type

== Rm ==
controls whether files and directories can be removed from the repository

== Rmattr ==
allows the user to remove an attribute

== Rmchangeset ==
controls whether a changeset can be removed from its branch

== Rmlabel ==
allows the user to remove a label

== Rmrepository ==
allows the user to remove repositories

== Rmtrigger ==
allows the user to remove a trigger

== SearchingForChanged ==
Searching for changed items in the workspace...

== Second ==
2nd

== SeidKey ==
Seid

== SelectorNoChanges ==
No changes.

== Server ==
Server

== SettingNewSelector ==
Setting the new selector...

== ShareRepoSucceeded ==
The repository '{0}' was correctly shared.

== Shelve ==
Shelve

== ShelvePendingChangesQuestion ==
There are pending changes in your workspace.
You can shelve and undo the pending changes, or cancel the switch.
If you choose to shelve your changes, they will be shelved and can be recovered later from the created shelveset.
If you choose to cancel the operation, the workspace will be left as is.
What would you like to do?

== ShelveOrBringPendingChangesQuestion ==
There are pending changes in your workspace.
You can shelve and undo the pending changes, bring your changes with you, or cancel the switch.
If you choose to shelve your changes, they will be shelved and can be recovered later from the created shelveset.
If you choose to bring your changes, they will be shelved and then automatically reapplied after the switch.
If you choose to cancel the operation, the workspace will be left as is.
What would you like to do?

== ShelveOrBringPendingChangesQuestionShelveOption ==
Save my changes (create a shelve)

== ShelveOrBringPendingChangesQuestionOverwriteShelvesWarning ==
Warning - you already have one or more automatic shelves created, that will be overwritten by the new one

== ShelveOrBringPendingChangesQuestionBringChangesOption ==
Bring my changes (create an intermediate shelve and apply it automatically)

== ShelvePendingChangesShelvesetComment ==
Automatic shelve created during switch operation

== ShelvePendingChangesShelvesetCommentFrom ==
(from {0})

== ShelveStatusConfirming ==
Confirming shelve operation

== ShelveStatusFinished ==
Shelve finished

== ShelveStatusRestoringPermissions ==
Restoring file access

== ShelveStatusStarting ==
Shelve operation starting...

== ShelveStatusUploading ==
Uploading file data

== Size ==
Size in bytes

== Skip ==
Skip

== Source ==
Source

== SourceDestinationChanged ==
The item {0} was changed on source but it has been discarded during merge because the content is the same on source and destination

== SourceDestinationChangedFsprotection ==
The item {0} has had its filesystem protections changed on source but it has been discarded during merge because they are the same on source and destination

== SourceDestinationDeleted ==
The item {0} was deleted on source but it has been discarded during merge because it was already deleted on destination

== SourceDestinationMoved ==
The file {0} was moved on source and destination to the same location so the move will be discarded. Moved from {1} to {2}

== SrcContributor ==
Source changeset: {0} (Branch {1})

== StartResolveConflict ==
Going to resolve the following conflict:

== StatColLastmod ==
Last Modified

== StatColPath ==
Path

== StatColSimilarity ==
Similarity

== StatColSize ==
Size

== StatColStatus ==
Status

== StatusKey ==
Status

== StatusLocked ==
Locked

== StatusPerfWarningChanged ==
Finding changed files took too long. Perf tips: {0}

== StatusPerfWarningMoved ==
Calculating moved files took too long. You have too many private/removed files. Perf tips: {0}

== StatusPerfWarningPrivates ==
You have too many privates and this can affect performance. Learn how to ignore private files: {0}

== StatusRetained ==
Retained

== SubtractiveIntervalMerge ==
Subtractive interval merge

== SubtractiveMerge ==
Subtractive merge

== SubtractiveNeeded ==
Subtractive merge needed on item {0} from {1} to {2} base {3}. Changed by {4}

== SupportBundleCreated ==
Support bundle created at {0}

== SupportBundleCreating ==
Creating a new support bundle...

== SupportContactUs ==
Remember you can reach us anytime at https://www.plasticscm.com/support.
Don't hesitate to get in touch if you have any questions, suggestions
or need guidance.


== SyncAlreadyReplicated ==
The sync cannot start because the target repository was replicated from a repository synchronized with git. The repository originally synchronized is '{0} - {1}'. Please contact support for further info.

== To ==
to

== Total ==
Total

== Purged ==
Purged

== TypeNewPassword ==
New password:

== TypeOldPassword ==
Old password:

== UnableToRemoveCset ==
The selected changeset {0} could not be deleted.

== UndoUnableToMove ==
Unable to move file '{0}' back to '{1}': {2}

You probably need to include deleted items before undoing your changes. Please check your current options:

- If you're using the CLI, try adding the deleted path or the "--deleted" argument to the 'undo' command.
- If you're using the GUI, open the Options dialog in the Pending Changes view and check "Show deleted files and directories" in the "What to show" tab.

== UndoneAddOperation ==
The local add operation {0} will be undone.

== UndoneDeleteOperation ==
The local delete operation {0} will be undone.

== UndoneMoveOperation ==
The local move operation {0} -> {1} will be undone.

== UnknownError ==
Unknown error

== Unlimited ==
Unlimited

== UnloadItemAlreadyChanged ==
The item won't be unloaded because it is already changed (checked-out, locally changed, deleted, moved or reverted) in the workspace: '{0}'. Please undo or checkin the local change and retry the operation.

== UnneededMergeRevisionLoaded ==
Copy merge for '{0}' is no longer needed because the correct revision is already loaded.

== UnresolvedXlink ==
unresolved xlink

== UnshareRepoSucceeded ==
The repository '{0}' was correctly unshared.

== UnsupportedMergeType ==
Unsupported merge type

== UpdateDeletedSelectorObjectSkipped ==
Unable to update your workspace to a valid branch configuration. It is likely that somebody else deleted the shelve or the label your workspace is set. Please switch your workspace to an existing branch, changeset, label or shelveset.

== UpdateProgress ==
Updated {0} of {1} ({2} of {3} files to download / {4} of {5} operations to apply)

== UpdateProgressCalculating ==
Calculating...

== UpdateProgressDownloadingBigFile ==
Downloading file {0} ({1}) from {2}

== UpdateProgressDownloadingBlock ==
Downloading block of {0} files ({1}) from {2}

== UpdateProgressUpdating ==
Updating...

== UpdateStatusCalculating ==
Calculating

== UpdateStatusFinished ==
Finished

== UpdateStatusUpdating ==
Updating

== UpdateWithChangesFromGluonWorkspaceNotAllowed ==
Cannot switch from a Gluon workspace to a standard one with checkouts. You can checkin from Gluon (or undo) and retry the update.

== UpdateWkIsUpToDate ==
The workspace {0} is up-to-date (cset:{1})

== UpdatingWorkspace ==
Unity VCS is updating your workspace. Wait a moment, please...

== UploadingFiles ==
Uploading {0} files

== User ==
User

== UserCorrectlyActivated ==
User {0} has been successfully activated

== UserCorrectlyDeactivated ==
User {0} has been successfully deactivated

== UserInformation ==
License information: server: {0}

== UserKey ==
User

== UserMultipleChoiceAnswerPrompt ==
Please enter your choice ({0}): 

== UserMultipleChoiceAnswerPromptWithDefaultOption ==
Please enter your choice ({0}) [default is '{1}']: 

== View ==
controls whether objects can be viewed

== VirtualPathDecorator ==
(virtual ancestor, revision on cs:{0})

== WaitingOperation ==
This operation can take several minutes. Wait, please ...

== Warning ==
Warning

== WhichChange ==
Which element do you want to change? (Source|Destination)[1|2]

== WhichRename ==
Which rename do you want to use? [1|2]

== WillCreateRepo ==
Repository {0} does not exist. It will be created

== WorkspacestatusAddGrp ==
Added items (AD = added, CP = copied (new), PR = private, IG = ignored)

== WorkspacestatusChGrp ==
Modified items (CH = changed, CO = checked-out, RP = replaced)

== WorkspacestatusMvGrp ==
Moved items (MV = moved, LM = locally moved)

== WorkspacestatusRmGrp ==
Deleted items (DE = deleted, LD = locally deleted)

== XlinkConflict ==
Xlink conflict

== XlinkConflictActions1 ==
Keep source changes in the Xlink

== XlinkConflictActions2 ==
Keep destination changes in the Xlink

== XlinkConflictDestinationOperation ==
Changed Xlink to: {0}

== XlinkConflictExplanation ==
A Xlink has been changed on source and destination.

== XlinkConflictSourceOperation ==
Changed Xlink to: {0}

== XlinkPointsToShelveTarget ==
The Xlink '{0}' won't be downloaded because it was created with new content inside. This is not supported yet.

== XlinkWritableConflict ==
Xlink missing ancestor conflict

== XlinkWritableConflictExplanation ==
The Xlinked repository doesn't know the ancestor changeset to calculate merge. The user must specify the ancestor changeset manually.

== Yes ==
Yes

== YesValidAnswers ==
y;yes

== LocalContentCheckedOut ==
LocalContentCheckedOut

== MovedHistDesc ==
Moved from {0} to {1}

== RemovedHistDesc ==
Removed {0}

== CmdConfigureErrorCannotCreateDir ==
Failed when creating a directory [{0}]:{1}

== CmdConfigureErrorWrongParameters ==
Wrong parameter number. Type 'cm configure --help' to get help

== CmdConfigureErrorReadingParameters ==
Error reading the parameters; please check the list of parameters provided.

== CmdConfigureHeader ==
####--- Client configuration wizard ---####

== CmdConfigureSuccess ==
The Unity VCS client has been correctly configured.

== CmdConfigureDetectedWorkingMode ==
Detected working mode: {0}

== CmdConfigureDetectedWorkingModeError ==
There was an error while checking the Unity VCS server security mode.
Maybe the server is offline, or the address specified is wrong.
Do you want to re-enter the Unity VCS server address?

== CmdConfigureServerParams ==
Configure the Unity VCS server address/port:

== CmdConfigureServerParamsAddress ==
Unity VCS server address [{0}]

== CmdConfigureServerParamsPort ==
Unity VCS server port [{0}]

== CmdConfigureServerParamsSslPort ==
Unity VCS SSL server port [{0}]

== CmdConfigureServerParamsUseSsl ==
Use encryption (SSL)?

== CmdConfigureProxyParams ==
Configure a proxy server?

== CmdConfigureProxyParamsAddress ==
Proxy server address

== CmdConfigureProxyParamsPort ==
Proxy server port

== CmdConfigureRetry ==
Do you want to retry?

== EncryptionConfigurationExplanation ==
The '{0}' source server requires data encryption.
Your server (aka '{1}') is not yet configured to handle encrypted data from it.

Remarks:
* Be very careful with the encryption password; it's the only way to decrypt your data on '{0}'.
* If you're decrypting data and you enter a wrong password, the data will fail to decrypt and you will have to edit cryptedservers.conf on your server to fix it.
* Be careful to use the same password (encryption key) company-wide to work with '{0}'. Otherwise, data will be encrypted differently in each server.

Enter a password to generate the encryption key (leave empty to cancel):

== EncryptionPasswordNotEntered ==
You didn't entered a password. Encryption won't be configured for server '{0}'.

== EncryptionConfigurationFailed ==
Failed to configure encryption for server '{0}'.

== EncryptionConfigurationSucceeded ==
Encryption configuration added for server '{0}'.
