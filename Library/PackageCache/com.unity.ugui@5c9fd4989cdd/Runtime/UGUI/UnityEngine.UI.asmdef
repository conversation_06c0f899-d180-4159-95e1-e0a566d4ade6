{"name": "UnityEngine.UI", "references": ["UnityEngine.UIElementsModule"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "PACKAGE_PHYSICS"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "PACKAGE_PHYSICS2D"}, {"name": "com.unity.modules.tilemap", "expression": "1.0.0", "define": "PACKAGE_TILEMAP"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "PACKAGE_ANIMATION"}, {"name": "com.unity.modules.uielements", "expression": "1.0.0", "define": "PACKAGE_UITOOLKIT"}, {"name": "com.unity.inputsystem", "expression": "1.7.0", "define": "PACKAGE_INPUTSYSTEM"}], "noEngineReferences": false}