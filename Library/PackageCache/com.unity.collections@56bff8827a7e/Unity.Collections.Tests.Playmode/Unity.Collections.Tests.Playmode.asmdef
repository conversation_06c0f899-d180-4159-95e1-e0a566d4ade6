{"name": "Unity.Collections.Tests.Playmode", "references": ["Unity.Entities", "Unity.Entities.Hybrid", "Unity.Entities.Tests", "Unity.Collections", "Unity.Burst", "Unity.Jobs", "Unity.Mathematics", "Unity.Collections.BurstCompatibilityGen"], "optionalUnityReferences": ["TestAssemblies"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["UNITY_DOTS_DEBUG"], "versionDefines": []}