# Custom Render Textures

With Custom Render Textures, you can use Shader Graph to create shaders that are compatible with Custom Render Texture Update and Initialization materials. The following topics describe how to access the shader properties and set up a Shader Graph for Custom Render Texture shaders.

| **Topic**                       | **Description**                  |
| :------------------------------ | :------------------------------- |
| **[Access Custom Render Texture shader properties](Custom-Render-Texture-Accessing.md)** | Access specific texture coordinates and other shader properties. |
| **[Example Custom Render Texture with Shader Graph](Custom-Render-Texture-Example.md)** | Set up a Shader Graph for Custom Render Texture shaders to create a self-healing deformation effect that could be used for snow, sand, etc. |

## Additional resources
[Custom Render Textures](https://docs.unity3d.com/Manual/class-CustomRenderTexture.html).
