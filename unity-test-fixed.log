[Licensing::Modu<PERSON>] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Channel LicenseClient-patryk doesn't exist
[Licensing::Module] Successfully launched the LicensingClient (PId: 47468)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-patryk" at "2025-07-03T07:57:57.0054191Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47468, path: "F:/Unity/Editor/6000.1.3f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.16.2+bdef8f5
  Session Id:              4bc5091150634871b9a02097ab03b695
  Correlation Id:          22df5d24fb98a30e2624be6abc7a345e
  External correlation Id: 9012950135980854726
  Machine Id:              3qpX9RBOm6ArecyxpFGyb6KeW74=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-patryk" (connect: 0.57s, validation: 0.05s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-patryk-notifications" at "2025-07-03T07:57:57.0699104Z"
[Licensing::Module] Connected to LicensingClient (PId: 47468, launch time: 0.00, total connection time: 0.63s)
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "9070991756955-UnityPersXXXX"
[Licensing::Client] Successfully updated license, isAync: True, time: 0.02
Built from '6000.1/staging' branch; Version is '6000.1.3f1 (f34db9734971) revision 15945145'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'de' Physical Memory: 63080 MB
[Licensing::Client] Successfully resolved entitlement details
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0
Date: 2025-07-03T07:57:57Z

COMMAND LINE ARGUMENTS:
F:\Unity\Editor\6000.1.3f1\Editor\Unity.exe
-runTests
-batchmode
-testResults
F:\Unity\Projects\Excidium\test-results-fixed.xml
-testPlatform
EditMode
-logFile
F:\Unity\Projects\Excidium\unity-test-fixed.log
F:/Unity/Projects/Excidium
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [47476]  Target information:

Player connection [47476]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 17842039 [EditorId] 17842039 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [47476]  * "[IP] ************* [Port] 55504 [Flags] 2 [Guid] 17842039 [EditorId] 17842039 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [47476]  * "[IP] *********** [Port] 55504 [Flags] 2 [Guid] 17842039 [EditorId] 17842039 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [47476] Host joined multi-casting on [***********:54997]...
Player connection [47476] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Package Manager] Connected to IPC stream "Upm-44752" after 0.2 seconds.
Library Redirect Path: Library/
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 58 packages:
  Packages from [https://packages.unity.com]:
    com.unity.ai.navigation@2.0.7 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ai.navigation@39ae74efb85f)
    com.unity.burst@1.8.21 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.burst@59eb6f11d242)
    com.unity.cinemachine@2.10.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.cinemachine@dcc61ebd6655)
    com.unity.collab-proxy@2.8.2 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f)
    com.unity.collections@2.5.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.collections@56bff8827a7e)
    com.unity.ide.rider@3.0.36 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db)
    com.unity.ide.visualstudio@2.0.23 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13)
    com.unity.inputsystem@1.14.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.inputsystem@7fe8299111a7)
    com.unity.mathematics@1.3.2 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.mathematics@8017b507cc74)
    com.unity.nuget.mono-cecil@1.11.4 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f)
    com.unity.package-validation-suite@0.22.0-preview (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.package-validation-suite@536239bd7458)
    com.unity.test-framework.performance@3.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed)
    com.unity.timeline@1.8.7 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.timeline@c58b4ee65782)
    com.unity.visualscripting@1.9.6 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.visualscripting@7dcdc439b230)
    com.unity.searcher@4.9.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.searcher@1e17ce91558d)
  Built-in packages:
    com.unity.ext.nunit@2.0.5 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ext.nunit@031a54704bff)
    com.unity.multiplayer.center@1.0.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546)
    com.unity.render-pipelines.core@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.core@79d3abb9adf1)
    com.unity.render-pipelines.universal@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.universal@35104b1b69f5)
    com.unity.render-pipelines.universal-config@17.0.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.universal-config@6af1487fecff)
    com.unity.rendering.light-transport@1.0.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.rendering.light-transport@ec31b4120e30)
    com.unity.shadergraph@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.shadergraph@26dc5ae27e7d)
    com.unity.test-framework@1.5.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.test-framework@4969648bc874)
    com.unity.ugui@2.0.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ugui@5c9fd4989cdd)
    com.unity.modules.accessibility@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director)
    com.unity.modules.hierarchycore@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore)
    com.unity.modules.imageconversion@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture)
    com.unity.modules.subsystems@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems)
    com.unity.modules.terrain@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr)
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.10 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.3f1 (f34db9734971)
[Subsystems] Discovering subsystems at path F:/Unity/Editor/6000.1.3f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Unity/Projects/Excidium/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon RX 7900 XT (ID=0x744c)
    Vendor:          ATI
    VRAM:            20464 MB
    App VRAM Budget: 19649 MB
    Driver:          32.0.21013.1000
[Licensing::Client] Successfully resolved entitlement details
Initialize mono
Mono path[0] = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56752
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity/Editor/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: F:/Unity/Editor/6000.1.3f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002349 seconds.
- Loaded All Assemblies, in  0.253 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.178 seconds
Domain Reload Profiling: 431ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (98ms)
		LoadAssemblies (95ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (96ms)
			TypeCache.Refresh (95ms)
				TypeCache.ScanAssembly (87ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (179ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (158ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (28ms)
			ProcessInitializeOnLoadAttributes (67ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
[ScriptCompilation] Requested script compilation because: Assetdatabase observed changes in script compilation related files
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Starting: F:\Unity\Editor\6000.1.3f1\Editor\Data\bee_backend.exe --ipc --defer-dag-verification --dagfile="Library/Bee/1900b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: F:/Unity/Projects/Excidium
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 1s27ms
[665/850    0s] ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput
[846/850    0s] Csc Library/Bee/artifacts/1900b0aE.dag/ExcidiumTests.dll (+2 others)
[847/850    0s] ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ExcidiumTests.dll (+pdb)
Processing assembly Library/Bee/artifacts/1900b0aE.dag/ExcidiumTests.dll, with 126 defines and 252 references

processors: Unity.Jobs.CodeGen.JobsILPostProcessor, zzzUnity.Burst.CodeGen.BurstILPostProcessor

running Unity.Jobs.CodeGen.JobsILPostProcessor

running zzzUnity.Burst.CodeGen.BurstILPostProcessor
[848/850    0s] CopyFiles Library/ScriptAssemblies/ExcidiumTests.dll
[849/850    0s] CopyFiles Library/ScriptAssemblies/ExcidiumTests.pdb
*** Tundra build success (1.01 seconds), 5 items updated, 850 evaluated
AssetDatabase: script compilation time: 1.305371s
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.616 seconds
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.054 seconds
Domain Reload Profiling: 2666ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (327ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (144ms)
			TypeCache.Refresh (108ms)
				TypeCache.ScanAssembly (99ms)
			BuildScriptInfoCaches (27ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (2054ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1903ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (1749ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Asset Pipeline Refresh (id=b7cef4fe80f8ef1478a2bed3530adac3): Total: 4.468 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2325 ms
		Asset DB Callback time: managed=165 ms, native=5 ms
		Scripting: domain reloads=1, domain reload time=616 ms, compile time=1306 ms, other=48 ms
		Project Asset Count: scripts=1323, non-scripts=2392
		Asset File Changes: new=0, changed=3, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 1.020ms
	ApplyChangesToAssetFolders: 0.351ms
	Scan: 72.900ms
	OnSourceAssetsModified: 0.752ms
	CategorizeAssetsWithTransientArtifact: 29.342ms
	ProcessAssetsWithTransientArtifactChanges: 45.710ms
	CategorizeAssets: 27.326ms
	ImportOutOfDateAssets: 2060.025ms (752.152ms without children)
		CompileScripts: 1306.410ms
		ReloadNativeAssets: 0.013ms
		UnloadImportedAssets: 0.823ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.335ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.291ms
	PostProcessAllAssets: 166.447ms
	Hotreload: 3.943ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.001ms
	UnloadStreamsBegin: 1.373ms
	PersistCurrentRevisions: 0.136ms
	UnloadStreamsEnd: 0.002ms
	GenerateScriptTypeHashes: 1.699ms
	Untracked: 2058.422ms

Application.AssetDatabase Initial Refresh End
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Scanning for USB devices : 35.045ms
Initializing Unity extensions:
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Excidium
Unloading 16 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6256 unused Assets / (4.8 MB). Loaded Objects now: 7019.
Memory consumption went from 217.3 MB to 212.6 MB.
Total: 8.101000 ms (FindLiveObjects: 0.440100 ms CreateObjectMapping: 0.161800 ms MarkObjects: 5.398300 ms  DeleteObjects: 2.099600 ms)

D3D11 device created for Microsoft Media Foundation video decoding.
[Project] Loading completed in 7.107 seconds
	Project init time: 				6.071 seconds
		Template init time: 		0.000 seconds
		Package Manager init time: 		0.220 seconds
		Asset Database init time: 		0.152 seconds
		Global illumination init time: 	0.002 seconds
		Assemblies load time: 			0.442 seconds
		Unity extensions init time: 	0.007 seconds
		Asset Database refresh time: 	4.468 seconds
	Scene opening time: 			0.296 seconds
##utp:{"type":"ProjectInfo","version":2,"phase":"Immediate","time":1751529483536,"processId":44752,"projectLoad":7.1074768,"projectInit":6.0711955,"templateInit":0.0,"packageManagerInit":0.2201235,"assetDatabaseInit":0.1522017,"globalIlluminationInit":0.0024419,"assembliesLoad":0.4423576,"unityExtensionsInit":0.0065156,"assetDatabaseRefresh":4.4678342,"sceneOpening":0.2962799}
##utp:{"type":"EditorInfo","version":2,"phase":"Immediate","time":1751529483536,"processId":44752,"editorVersion":"6000.1.3f1 (f34db9734971)","branch":"6000.1/staging","buildType":"Release","platform":"Windows"}
Asset Pipeline Refresh (id=0fe4b58ed70724040ac329ee044f3101): Total: 0.034 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
Running tests for ExecutionSettings with details:
targetPlatform = 
playerHeartbeatTimeout = 600
filters[0] = 
   Filter with settings:
   testMode = EditMode
   targetPlatform = 
   testNames = null
   groupNames = null
   categoryNames = null
   assemblyNames = null
ignoreTests = {}
featureFlags = null

UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:InitializeAndExecuteRun (string[]) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:53)
UnityEditor.TestTools.TestRunner.CommandLineTest.TestStarter:InitializeAndExecuteRun () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/TestStarter.cs:64)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs Line: 53)

Couldn't find a readme
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ReadmeEditor:SelectReadme () (at Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs:90)
ReadmeEditor:SelectReadmeAutomatically () (at Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs:58)
UnityEditor.EditorApplication:Internal_CallDelayFunctions ()

(Filename: Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs Line: 90)

[UnityConnectServicesConfig] config is NOT valid, switching to default
Created GICache directory at C:/Users/<USER>/AppData/LocalLow/Unity/Caches/GiCache. Took: 0.015s, timestamps: [7.116 - 7.131]
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1 unused Assets / (0.5 KB). Loaded Objects now: 7064.
Memory consumption went from 215.7 MB to 215.7 MB.
Total: 5.590800 ms (FindLiveObjects: 0.283900 ms CreateObjectMapping: 0.066300 ms MarkObjects: 5.225200 ms  DeleteObjects: 0.014700 ms)

Executing IPrebuildSetup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<ExecuteMethods>d__8<UnityEngine.TestTools.IPrebuildSetup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:51)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<UnityEngine.TestTools.IPrebuildSetup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:35)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs Line: 51)

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<ChunkBoundaryVoxels_ShouldRenderCorrectly>d__14:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:268)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<EmptyChunk_ShouldHandleGracefully>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:209)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<FullChunk_ShouldOnlyRenderExteriorFaces>d__13:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:235)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<LargeChunk_ShouldGenerateWithinReasonableTime>d__8:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:110)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<MeshCollider_ShouldBeUpdatedWithMesh>d__7:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:88)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<MeshNormals_ShouldBeCalculatedCorrectly>d__11:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:170)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating material due to calling renderer.material during edit mode. This will leak materials into the scene. You most likely want to use renderer.sharedMaterial instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Renderer:GetMaterial ()
UnityEngine.Renderer:get_material ()
ExcidiumTests.VoxelChunkIntegrationTests/<MeshRenderer_ShouldHaveMaterialAssigned>d__10:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:156)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<RapidModifications_ShouldNotCauseErrors>d__15:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:301)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<RuntimeModification_ShouldUpdateMeshCorrectly>d__6:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:60)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkIntegrationTests/<VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame>d__5:MoveNext () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:50)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkPerformanceTests/<FaceCulling_ShouldSignificantlyReduceTriangleCount>d__9:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:189)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<GenerationTime_ShouldScaleReasonablyWithSize>d__10:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:234)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Large chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<LargeChunk_ShouldGenerateWithinReasonableTime>d__6:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:91)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 91)

Medium chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<MediumChunk_ShouldGenerateWithinBudget>d__5:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:73)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 73)

Memory used for chunk generation: 0,00MB
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<MeshGeneration_ShouldNotExcessivelyAllocateMemory>d__7:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:125)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 125)

Memory difference after regenerations: 0,29MB
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<MultipleRegenerations_ShouldNotLeakMemory>d__8:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:161)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 161)

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkPerformanceTests/<PerformanceRegression_ShouldMaintainBaselinePerformance>d__11:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:280)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Performance baseline check - Time: 0ms, Triangles/Voxel: 0,6
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<PerformanceRegression_ShouldMaintainBaselinePerformance>d__11:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:289)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 289)

Small chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<SmallChunk_ShouldGenerateQuickly>d__4:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:55)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 55)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:338)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:341)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:338)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:341)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:338)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:341)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:338)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:341)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces () (at Assets/ExcidiumTests/VoxelChunkTests.cs:319)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:FaceGeneration_ShouldHaveCorrectWindingOrder () (at Assets/ExcidiumTests/VoxelChunkTests.cs:219)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshBounds_ShouldMatchChunkSize () (at Assets/ExcidiumTests/VoxelChunkTests.cs:364)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshGeneration_WhenCalled_ShouldCreateValidMesh () (at Assets/ExcidiumTests/VoxelChunkTests.cs:150)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount () (at Assets/ExcidiumTests/VoxelChunkTests.cs:184)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:RegenerateChunk_ShouldProduceSameResultAsInitialGeneration () (at Assets/ExcidiumTests/VoxelChunkTests.cs:342)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:RegenerateChunk_ShouldProduceSameResultAsInitialGeneration () (at Assets/ExcidiumTests/VoxelChunkTests.cs:349)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:TopFace_ShouldPointUpward () (at Assets/ExcidiumTests/VoxelChunkTests.cs:266)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
Saving results to: F:\Unity\Projects\Excidium\test-results-fixed.xml
Executing IPostBuildCleanup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<ExecuteMethods>d__8<UnityEngine.TestTools.IPostBuildCleanup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:51)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<UnityEngine.TestTools.IPostBuildCleanup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:35)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs Line: 51)

Asset Pipeline Refresh (id=f74aaae4f41fa514eb992ee0a4a7fac6): Total: 0.003 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1 unused Assets / (0 B). Loaded Objects now: 7065.
Memory consumption went from 206.8 MB to 206.8 MB.
Total: 5.926900 ms (FindLiveObjects: 0.200300 ms CreateObjectMapping: 0.066500 ms MarkObjects: 5.640900 ms  DeleteObjects: 0.018100 ms)

DisplayProgressbar: Undo
Test run completed. Exiting with code 2 (Failed). One or more tests failed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:ExitApplication (UnityEditor.TestTools.TestRunner.CommandLineTest.Executer/ReturnCodes,string) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:81)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:ExitIfRunIsCompleted () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:74)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs Line: 81)

[Physics::Module] Cleanup current backned.
[Physics::Module] Id: 0xf2b8ea05
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-patryk-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-patryk channel disconnected successfully.
AcceleratorClientConnectionCallback - disconnected - :0
Cleanup mono
09:58:05.239 |V| RiderPlugin                    | :1                             | AppDomain.CurrentDomain.DomainUnload lifetimeDefinition.Terminate
Checking for leaked weakptr:
  Found no leaked weakptrs.
[Package Manager] Server process was shutdown
##utp:{"type":"MemoryLeaks","version":2,"phase":"Immediate","time":1751529485567,"processId":44752,"allocatedMemory":14360918,"memoryLabels":[{"Default":14269},{"Permanent":15412},{"NewDelete":68265},{"Thread":4237960},{"Manager":30682},{"VertexData":8},{"Geometry":816},{"VFX":-104},{"Texture":296},{"Shader":115649},{"Material":24},{"GfxDevice":57248},{"Animation":328},{"Audio":4040},{"FontEngine":256},{"Physics":1241},{"Serialization":880},{"Input":14824},{"JobScheduler":55888},{"TextLib":160},{"Mono":32},{"ScriptingNativeRuntime":155060},{"BaseObject":1619952},{"Resource":1304},{"Renderer":2928},{"Transform":4646},{"File":1016},{"WebCam":616},{"Culling":32},{"Terrain":11394},{"Wind":24},{"STL":40},{"String":9419},{"DynamicArray":116703},{"HashMap":105551},{"Utility":2667110},{"Curl":48},{"PoolAlloc":2056},{"AI":40},{"TypeTree":5005},{"ScriptManager":512},{"RuntimeInitializeOnLoadManager":64},{"SpriteAtlas":112},{"GI":7824},{"Director":8240},{"WebRequest":808},{"VR":46058},{"SceneManager":496},{"Video":208},{"LazyScriptCache":32},{"NativeArray":8},{"Camera":17},{"Secure":1},{"SerializationCache":1576},{"APIUpdating":10320},{"Subsystems":336},{"VirtualTexturing":57888},{"StaticSafetyDebugInfo":278528},{"Analytics":256},{"Hierarchy":272},{"Gui":80},{"EditorUtility":107338},{"VersionControl":4},{"Undo":947},{"AssetDatabase":4473442},{"EditorGi":328},{"UnityConnect":24536},{"Upm":1972},{"DrivenProperties":72},{"LocalIPC":262},{"ProfilerEditor":9738},{"CoreBusinessMetrics":3949},{"Licensing":3512},{"AssetReference":32},{"IPCStream":32}]}
