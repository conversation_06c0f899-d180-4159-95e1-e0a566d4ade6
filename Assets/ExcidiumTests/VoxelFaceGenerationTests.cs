using NUnit.Framework;
using UnityEngine;
using Excidium;

namespace ExcidiumTests
{
    /// <summary>
    /// Comprehensive tests for voxel face generation algorithm.
    /// These tests verify that face normals point outward correctly for all 6 faces of a voxel.
    /// </summary>
    public class VoxelFaceGenerationTests
    {
        private GameObject _testObject;
        private VoxelChunk _voxelChunk;
        private const int TestChunkSize = 4;
        private const float FloatTolerance = 0.001f;

        [SetUp]
        public void SetUp()
        {
            _testObject = new GameObject("TestVoxelChunk");
            _voxelChunk = _testObject.AddComponent<VoxelChunk>();
            _voxelChunk.chunkSize = TestChunkSize;
        }

        [TearDown]
        public void TearDown()
        {
            if (_testObject != null)
                Object.DestroyImmediate(_testObject);
        }

        /// <summary>
        /// Test that top and bottom faces have correct outward-pointing normals.
        /// This test should pass with the current implementation.
        /// </summary>
        [Test]
        public void TopAndBottomFaces_ShouldPointOutward()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            Assert.Greater(mesh.vertices.Length, 0, "Mesh should have vertices");
            
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundTopFace = false;
            bool foundBottomFace = false;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check top face (Y = 2 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.y - 2.0f) < FloatTolerance)
                {
                    Assert.Greater(normal.y, 0.8f, $"Top face normal should point upward. Vertex: {vertex}, Normal: {normal}");
                    foundTopFace = true;
                }
                
                // Check bottom face (Y = 1 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.y - 1.0f) < FloatTolerance)
                {
                    Assert.Less(normal.y, -0.8f, $"Bottom face normal should point downward. Vertex: {vertex}, Normal: {normal}");
                    foundBottomFace = true;
                }
            }
            
            Assert.IsTrue(foundTopFace, "Should find top face vertices");
            Assert.IsTrue(foundBottomFace, "Should find bottom face vertices");
        }

        /// <summary>
        /// Test that front face (+Z) has correct outward-pointing normal.
        /// This test will likely fail due to incorrect vertex ordering.
        /// </summary>
        [Test]
        public void FrontFace_ShouldPointOutward()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundFrontFace = false;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check front face (Z = 2 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.z - 2.0f) < FloatTolerance)
                {
                    Assert.Greater(normal.z, 0.8f, $"Front face normal should point forward (+Z). Vertex: {vertex}, Normal: {normal}");
                    foundFrontFace = true;
                }
            }
            
            Assert.IsTrue(foundFrontFace, "Should find front face vertices");
        }

        /// <summary>
        /// Test that back face (-Z) has correct outward-pointing normal.
        /// This test will likely fail due to incorrect vertex ordering.
        /// </summary>
        [Test]
        public void BackFace_ShouldPointOutward()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundBackFace = false;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check back face (Z = 1 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.z - 1.0f) < FloatTolerance)
                {
                    Assert.Less(normal.z, -0.8f, $"Back face normal should point backward (-Z). Vertex: {vertex}, Normal: {normal}");
                    foundBackFace = true;
                }
            }
            
            Assert.IsTrue(foundBackFace, "Should find back face vertices");
        }

        /// <summary>
        /// Test that right face (+X) has correct outward-pointing normal.
        /// This test will likely fail due to incorrect vertex ordering.
        /// </summary>
        [Test]
        public void RightFace_ShouldPointOutward()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundRightFace = false;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check right face (X = 2 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.x - 2.0f) < FloatTolerance)
                {
                    Assert.Greater(normal.x, 0.8f, $"Right face normal should point right (+X). Vertex: {vertex}, Normal: {normal}");
                    foundRightFace = true;
                }
            }
            
            Assert.IsTrue(foundRightFace, "Should find right face vertices");
        }

        /// <summary>
        /// Test that left face (-X) has correct outward-pointing normal.
        /// This test will likely fail due to incorrect vertex ordering.
        /// </summary>
        [Test]
        public void LeftFace_ShouldPointOutward()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundLeftFace = false;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check left face (X = 1 for voxel at position (1,1,1))
                if (Mathf.Abs(vertex.x - 1.0f) < FloatTolerance)
                {
                    Assert.Less(normal.x, -0.8f, $"Left face normal should point left (-X). Vertex: {vertex}, Normal: {normal}");
                    foundLeftFace = true;
                }
            }
            
            Assert.IsTrue(foundLeftFace, "Should find left face vertices");
        }

        /// <summary>
        /// Comprehensive test that checks all 6 faces at once and provides detailed failure information.
        /// </summary>
        [Test]
        public void AllFaces_ShouldPointOutward_ComprehensiveTest()
        {
            // Arrange - Create a single isolated voxel
            _voxelChunk.Initialize();
            ClearAllVoxels();
            _voxelChunk.SetVoxel(1, 1, 1, true);
            
            // Act
            _voxelChunk.RegenerateMesh();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            
            // Assert
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            var faceResults = new System.Text.StringBuilder();
            faceResults.AppendLine("Face normal analysis for voxel at (1,1,1):");
            
            int topFaceCount = 0, bottomFaceCount = 0, frontFaceCount = 0;
            int backFaceCount = 0, rightFaceCount = 0, leftFaceCount = 0;
            
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Analyze each face
                if (Mathf.Abs(vertex.y - 2.0f) < FloatTolerance) // Top face
                {
                    topFaceCount++;
                    faceResults.AppendLine($"Top face vertex {i}: {vertex}, normal: {normal}, Y-component: {normal.y}");
                }
                else if (Mathf.Abs(vertex.y - 1.0f) < FloatTolerance) // Bottom face
                {
                    bottomFaceCount++;
                    faceResults.AppendLine($"Bottom face vertex {i}: {vertex}, normal: {normal}, Y-component: {normal.y}");
                }
                else if (Mathf.Abs(vertex.z - 2.0f) < FloatTolerance) // Front face
                {
                    frontFaceCount++;
                    faceResults.AppendLine($"Front face vertex {i}: {vertex}, normal: {normal}, Z-component: {normal.z}");
                }
                else if (Mathf.Abs(vertex.z - 1.0f) < FloatTolerance) // Back face
                {
                    backFaceCount++;
                    faceResults.AppendLine($"Back face vertex {i}: {vertex}, normal: {normal}, Z-component: {normal.z}");
                }
                else if (Mathf.Abs(vertex.x - 2.0f) < FloatTolerance) // Right face
                {
                    rightFaceCount++;
                    faceResults.AppendLine($"Right face vertex {i}: {vertex}, normal: {normal}, X-component: {normal.x}");
                }
                else if (Mathf.Abs(vertex.x - 1.0f) < FloatTolerance) // Left face
                {
                    leftFaceCount++;
                    faceResults.AppendLine($"Left face vertex {i}: {vertex}, normal: {normal}, X-component: {normal.x}");
                }
            }
            
            faceResults.AppendLine($"Face counts - Top: {topFaceCount}, Bottom: {bottomFaceCount}, Front: {frontFaceCount}, Back: {backFaceCount}, Right: {rightFaceCount}, Left: {leftFaceCount}");
            
            // Log the analysis for debugging
            Debug.Log(faceResults.ToString());
            
            // This test is designed to show the current state - it will likely fail for side faces
            Assert.AreEqual(24, vertices.Length, "Single voxel should have 24 vertices (6 faces × 4 vertices)");
            Assert.AreEqual(4, topFaceCount, "Top face should have 4 vertices");
            Assert.AreEqual(4, bottomFaceCount, "Bottom face should have 4 vertices");
            Assert.AreEqual(4, frontFaceCount, "Front face should have 4 vertices");
            Assert.AreEqual(4, backFaceCount, "Back face should have 4 vertices");
            Assert.AreEqual(4, rightFaceCount, "Right face should have 4 vertices");
            Assert.AreEqual(4, leftFaceCount, "Left face should have 4 vertices");
        }

        private void ClearAllVoxels()
        {
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }
        }
    }
}
