using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Excidium;

namespace ExcidiumTests
{
    /// <summary>
    /// Integration tests for VoxelChunk that require Play Mode
    /// Tests runtime behavior, performance, and Unity-specific functionality
    /// </summary>
    public class VoxelChunkIntegrationTests
    {
        private GameObject _testGameObject;
        private VoxelChunk _voxelChunk;
        private const int TestChunkSize = 8;

        [SetUp]
        public void SetUp()
        {
            _testGameObject = new GameObject("TestVoxelChunk");
            _voxelChunk = _testGameObject.AddComponent<VoxelChunk>();
            _voxelChunk.chunkSize = TestChunkSize;
            _voxelChunk.showDebugInfo = false;
            _voxelChunk.Initialize(); // Initialize components for edit mode testing
        }

        [TearDown]
        public void TearDown()
        {
            if (_testGameObject != null)
            {
                Object.DestroyImmediate(_testGameObject);
            }
        }

        #region Runtime Behavior Tests

        [UnityTest]
        public IEnumerator VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame()
        {
            // Arrange - VoxelChunk is already set up in SetUp()

            // Act - Wait one frame for Start() to be called
            yield return null;

            // Assert
            var meshFilter = _voxelChunk.GetComponent<MeshFilter>();
            Assert.IsNotNull(meshFilter.sharedMesh, "Mesh should be generated after Start()");
            Assert.Greater(meshFilter.sharedMesh.vertices.Length, 0, "Mesh should have vertices");
        }

        [UnityTest]
        public IEnumerator RuntimeModification_ShouldUpdateMeshCorrectly()
        {
            // Arrange
            yield return null; // Wait for initial generation

            var initialMesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            var initialVertexCount = initialMesh.vertices.Length;

            // Act - Modify voxels and regenerate
            _voxelChunk.SetVoxel(1, 1, 1, false); // Remove a voxel
            _voxelChunk.SetVoxel(2, 2, 2, true);  // Add a voxel
            _voxelChunk.RegenerateChunk();

            yield return null; // Wait one frame

            // Assert
            var modifiedMesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            Assert.IsNotNull(modifiedMesh, "Modified mesh should exist");
            // Vertex count might be different due to face culling changes
            Assert.Greater(modifiedMesh.vertices.Length, 0, "Modified mesh should have vertices");
        }

        [UnityTest]
        public IEnumerator MeshCollider_ShouldBeUpdatedWithMesh()
        {
            // Arrange & Act
            yield return null; // Wait for generation

            // Assert
            var meshCollider = _voxelChunk.GetComponent<MeshCollider>();
            var meshFilter = _voxelChunk.GetComponent<MeshFilter>();
            
            Assert.IsNotNull(meshCollider.sharedMesh, "MeshCollider should have a mesh");
            Assert.AreSame(meshFilter.sharedMesh, meshCollider.sharedMesh,
                "MeshCollider should use the same mesh as MeshFilter");
        }

        #endregion

        #region Performance Tests

        [UnityTest]
        public IEnumerator LargeChunk_ShouldGenerateWithinReasonableTime()
        {
            // Arrange - Create a larger chunk
            _voxelChunk.chunkSize = 16; // Larger chunk for performance testing
            var startTime = Time.realtimeSinceStartup;

            // Act
            yield return null; // Wait for generation

            // Assert
            var generationTime = Time.realtimeSinceStartup - startTime;
            Assert.Less(generationTime, 1.0f, $"Large chunk generation took too long: {generationTime:F3}s");
            
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            Assert.IsNotNull(mesh, "Large chunk should generate a valid mesh");
        }

        [UnityTest]
        public IEnumerator MultipleRegenerations_ShouldNotCausePerformanceDegradation()
        {
            // Arrange
            yield return null; // Initial generation

            var times = new float[5];

            // Act - Perform multiple regenerations and measure time
            for (int i = 0; i < times.Length; i++)
            {
                var startTime = Time.realtimeSinceStartup;
                _voxelChunk.RegenerateChunk();
                yield return null;
                times[i] = Time.realtimeSinceStartup - startTime;
            }

            // Assert - Later regenerations shouldn't be significantly slower
            var firstTime = times[0];
            var lastTime = times[times.Length - 1];
            var performanceRatio = lastTime / firstTime;
            
            Assert.Less(performanceRatio, 2.0f, 
                $"Performance degradation detected. First: {firstTime:F4}s, Last: {lastTime:F4}s, Ratio: {performanceRatio:F2}");
        }

        #endregion

        #region Material and Rendering Tests

        [UnityTest]
        public IEnumerator MeshRenderer_ShouldHaveMaterialAssigned()
        {
            // Arrange - Create a new chunk with material set before initialization
            Object.DestroyImmediate(_testGameObject);
            _testGameObject = new GameObject("TestVoxelChunkWithMaterial");
            _voxelChunk = _testGameObject.AddComponent<VoxelChunk>();
            _voxelChunk.chunkSize = TestChunkSize;
            _voxelChunk.showDebugInfo = false;

            var testMaterial = new Material(Shader.Find("Standard"));
            _voxelChunk.chunkMaterial = testMaterial;
            _voxelChunk.Initialize(); // Initialize with material set

            // Act
            yield return null; // Wait for generation

            // Assert
            var meshRenderer = _voxelChunk.GetComponent<MeshRenderer>();
            Assert.IsNotNull(meshRenderer.sharedMaterial, "MeshRenderer should have a material");
            Assert.AreSame(testMaterial, meshRenderer.sharedMaterial, "Should use assigned chunk material");

            // Cleanup
            Object.DestroyImmediate(testMaterial);
        }

        [UnityTest]
        public IEnumerator MeshNormals_ShouldBeCalculatedCorrectly()
        {
            // Arrange & Act
            yield return null; // Wait for generation

            // Assert
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            var normals = mesh.normals;
            
            Assert.AreEqual(mesh.vertices.Length, normals.Length, 
                "Normal count should match vertex count");
            
            // Check that normals are normalized
            foreach (var normal in normals)
            {
                var magnitude = normal.magnitude;
                Assert.That(magnitude, Is.EqualTo(1.0f).Within(0.01f), 
                    $"Normal should be normalized. Magnitude: {magnitude}");
            }
        }

        #endregion

        #region Edge Case Tests

        [UnityTest]
        public IEnumerator EmptyChunk_ShouldHandleGracefully()
        {
            // Arrange - Create chunk with no solid voxels
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }

            // Act - Regenerate only the mesh, not the terrain
            _voxelChunk.RegenerateMesh();
            yield return null;

            // Assert
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            Assert.IsNotNull(mesh, "Empty chunk should still have a mesh object");
            Assert.AreEqual(0, mesh.vertices.Length, "Empty chunk should have no vertices");
            Assert.AreEqual(0, mesh.triangles.Length, "Empty chunk should have no triangles");
        }

        [UnityTest]
        public IEnumerator FullChunk_ShouldOnlyRenderExteriorFaces()
        {
            // Arrange - Create chunk with all solid voxels
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, true);
                    }
                }
            }

            // Act - Regenerate only the mesh, not the terrain
            _voxelChunk.RegenerateMesh();
            yield return null;

            // Assert
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;

            // Full chunk should only have exterior faces
            // 6 faces per side of the cube: 6 * (chunkSize^2) * 4 vertices per face
            var expectedVertices = 6 * TestChunkSize * TestChunkSize * 4;
            
            Assert.AreEqual(expectedVertices, mesh.vertices.Length,
                $"Full chunk should have {expectedVertices} vertices (only exterior faces), got {mesh.vertices.Length}");
        }

        [UnityTest]
        public IEnumerator ChunkBoundaryVoxels_ShouldRenderCorrectly()
        {
            // Arrange - Set voxels only at chunk boundaries
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        bool isBoundary = x == 0 || x == TestChunkSize - 1 ||
                                         y == 0 || y == TestChunkSize - 1 ||
                                         z == 0 || z == TestChunkSize - 1;
                        _voxelChunk.SetVoxel(x, y, z, isBoundary);
                    }
                }
            }

            // Act
            _voxelChunk.RegenerateChunk();
            yield return null;

            // Assert
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            Assert.Greater(mesh.vertices.Length, 0, "Boundary voxels should generate mesh");
            Assert.Greater(mesh.triangles.Length, 0, "Boundary voxels should generate triangles");
        }

        #endregion

        #region Stress Tests

        [UnityTest]
        public IEnumerator RapidModifications_ShouldNotCauseErrors()
        {
            // Arrange
            yield return null; // Initial generation

            // Act - Rapidly modify voxels
            for (int i = 0; i < 10; i++)
            {
                var x = Random.Range(0, TestChunkSize);
                var y = Random.Range(0, TestChunkSize);
                var z = Random.Range(0, TestChunkSize);
                var solid = Random.value > 0.5f;
                
                _voxelChunk.SetVoxel(x, y, z, solid);
                
                if (i % 3 == 0) // Regenerate every few modifications
                {
                    _voxelChunk.RegenerateChunk();
                    yield return null;
                }
            }

            // Assert - Should not have thrown any exceptions
            var mesh = _voxelChunk.GetComponent<MeshFilter>().sharedMesh;
            Assert.IsNotNull(mesh, "Mesh should still exist after rapid modifications");
        }

        #endregion

        #region Validation Helper Tests

        [UnityTest]
        public IEnumerator DebugValidation_WhenEnabled_ShouldNotThrowErrors()
        {
            // Arrange
            _voxelChunk.showDebugInfo = true;

            // Act & Assert - Should not throw exceptions during validation
            LogAssert.NoUnexpectedReceived(); // Ensure no error logs
            yield return null; // Wait for generation with debug enabled
            
            // Validation should run without errors
            Assert.IsTrue(true, "Debug validation completed without errors");
        }

        #endregion
    }
}
