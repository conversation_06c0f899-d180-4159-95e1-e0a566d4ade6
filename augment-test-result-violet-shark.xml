<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="34" result="Failed(Child)" total="34" passed="31" failed="3" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0,7943535">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0.794354" total="34" passed="31" failed="3" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1038" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0.790657" total="34" passed="31" failed="3" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="29312" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1039" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0.790482" total="34" passed="31" failed="3" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelChunkIntegrationTests" fullname="ExcidiumTests.VoxelChunkIntegrationTests" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" testcasecount="12" result="Failed" site="Child" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.049834" total="12" passed="11" failed="1" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1011" name="ChunkBoundaryVoxels_ShouldRenderCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.ChunkBoundaryVoxels_ShouldRenderCorrectly" methodname="ChunkBoundaryVoxels_ShouldRenderCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="3869702" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.016345" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1013" name="DebugValidation_WhenEnabled_ShouldNotThrowErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.DebugValidation_WhenEnabled_ShouldNotThrowErrors" methodname="DebugValidation_WhenEnabled_ShouldNotThrowErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="931012321" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.001531" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1009" name="EmptyChunk_ShouldHandleGracefully" fullname="ExcidiumTests.VoxelChunkIntegrationTests.EmptyChunk_ShouldHandleGracefully" methodname="EmptyChunk_ShouldHandleGracefully" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1431585162" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.004347" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1010" name="FullChunk_ShouldOnlyRenderExteriorFaces" fullname="ExcidiumTests.VoxelChunkIntegrationTests.FullChunk_ShouldOnlyRenderExteriorFaces" methodname="FullChunk_ShouldOnlyRenderExteriorFaces" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="531426076" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.001681" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1005" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkIntegrationTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1301289257" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.001747" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1004" name="MeshCollider_ShouldBeUpdatedWithMesh" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshCollider_ShouldBeUpdatedWithMesh" methodname="MeshCollider_ShouldBeUpdatedWithMesh" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1228456949" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.001321" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1008" name="MeshNormals_ShouldBeCalculatedCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshNormals_ShouldBeCalculatedCorrectly" methodname="MeshNormals_ShouldBeCalculatedCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1521438980" result="Failed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.003728" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Normal should be normalized. Magnitude: 0
  Expected: 1.0f +/- 0.00999999978f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkIntegrationTests+<MeshNormals_ShouldBeCalculatedCorrectly>d__11.MoveNext () [0x00092] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkIntegrationTests.cs:190
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in .\Library\PackageCache\com.unity.test-framework@4969648bc874\UnityEngine.TestRunner\NUnitExtensions\Attributes\TestEnumerator.cs:44
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1007" name="MeshRenderer_ShouldHaveMaterialAssigned" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshRenderer_ShouldHaveMaterialAssigned" methodname="MeshRenderer_ShouldHaveMaterialAssigned" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1041021088" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.004117" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1006" name="MultipleRegenerations_ShouldNotCausePerformanceDegradation" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MultipleRegenerations_ShouldNotCausePerformanceDegradation" methodname="MultipleRegenerations_ShouldNotCausePerformanceDegradation" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1785640536" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.002744" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1012" name="RapidModifications_ShouldNotCauseErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RapidModifications_ShouldNotCauseErrors" methodname="RapidModifications_ShouldNotCauseErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1858461475" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.002278" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1003" name="RuntimeModification_ShouldUpdateMeshCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RuntimeModification_ShouldUpdateMeshCorrectly" methodname="RuntimeModification_ShouldUpdateMeshCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1401416233" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.001160" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1002" name="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" fullname="ExcidiumTests.VoxelChunkIntegrationTests.VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" methodname="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1768386929" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.000785" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1014" name="VoxelChunkPerformanceTests" fullname="ExcidiumTests.VoxelChunkPerformanceTests" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" testcasecount="9" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0.545889" total="9" passed="9" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-case id="1020" name="FaceCulling_ShouldSignificantlyReduceTriangleCount" fullname="ExcidiumTests.VoxelChunkPerformanceTests.FaceCulling_ShouldSignificantlyReduceTriangleCount" methodname="FaceCulling_ShouldSignificantlyReduceTriangleCount" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="403853614" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.003366" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Face culling effectiveness - Isolated: 768 triangles, Connected: 192 triangles, Reduction: 75,0%
]]></output>
          </test-case>
          <test-case id="1021" name="GenerationTime_ShouldScaleReasonablyWithSize" fullname="ExcidiumTests.VoxelChunkPerformanceTests.GenerationTime_ShouldScaleReasonablyWithSize" methodname="GenerationTime_ShouldScaleReasonablyWithSize" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="356510106" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.003044" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Chunk size 4�: 0ms
Chunk size 8�: 0ms
Chunk size 16�: 0ms
]]></output>
          </test-case>
          <test-case id="1017" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkPerformanceTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1060841772" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.002232" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Large chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1016" name="MediumChunk_ShouldGenerateWithinBudget" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MediumChunk_ShouldGenerateWithinBudget" methodname="MediumChunk_ShouldGenerateWithinBudget" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="301465449" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.002228" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Medium chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1018" name="MeshGeneration_ShouldNotExcessivelyAllocateMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MeshGeneration_ShouldNotExcessivelyAllocateMemory" methodname="MeshGeneration_ShouldNotExcessivelyAllocateMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="343821153" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:08Z" duration="0.178326" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory used for chunk generation: 0,00MB
]]></output>
          </test-case>
          <test-case id="1019" name="MultipleRegenerations_ShouldNotLeakMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MultipleRegenerations_ShouldNotLeakMemory" methodname="MultipleRegenerations_ShouldNotLeakMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1415767684" result="Passed" start-time="2025-07-06 19:39:08Z" end-time="2025-07-06 19:39:09Z" duration="0.347178" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory difference after regenerations: -0,16MB
]]></output>
          </test-case>
          <test-case id="1022" name="PerformanceRegression_ShouldMaintainBaselinePerformance" fullname="ExcidiumTests.VoxelChunkPerformanceTests.PerformanceRegression_ShouldMaintainBaselinePerformance" methodname="PerformanceRegression_ShouldMaintainBaselinePerformance" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1158575144" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.002526" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Performance baseline check - Time: 0ms, Triangles/Voxel: 0,6
]]></output>
          </test-case>
          <test-case id="1015" name="SmallChunk_ShouldGenerateQuickly" fullname="ExcidiumTests.VoxelChunkPerformanceTests.SmallChunk_ShouldGenerateQuickly" methodname="SmallChunk_ShouldGenerateQuickly" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1168916352" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.001883" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Small chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1023" name="StressTest_MultipleChunksSimultaneously" fullname="ExcidiumTests.VoxelChunkPerformanceTests.StressTest_MultipleChunksSimultaneously" methodname="StressTest_MultipleChunksSimultaneously" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="808532628" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.004073" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Stress test completed - 4 chunks in 1ms
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1024" name="VoxelChunkTests" fullname="ExcidiumTests.VoxelChunkTests" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.194025" total="13" passed="11" failed="2" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1034" name="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" fullname="ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" methodname="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1218076137" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.001800" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1032" name="FaceGeneration_ShouldHaveCorrectWindingOrder" fullname="ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder" methodname="FaceGeneration_ShouldHaveCorrectWindingOrder" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="686628469" result="Failed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.005454" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Triangle 0 has incorrect winding order. Dot product: 0
  Expected: greater than 0.5f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder () [0x00127] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:242
]]></stack-trace>
            </failure>
            <output><![CDATA[Mesh has zero-magnitude normals after RecalculateNormals()
Triangle 0: v0=(1.00, 2.00, 1.00), v1=(1.00, 2.00, 2.00), v2=(2.00, 2.00, 1.00), calculated normal=(0.00, 1.00, 0.00), magnitude=1
Triangle 1: v0=(1.00, 2.00, 1.00), v1=(2.00, 2.00, 2.00), v2=(1.00, 2.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 2: v0=(1.00, 1.00, 1.00), v1=(2.00, 1.00, 1.00), v2=(2.00, 1.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 3: v0=(1.00, 1.00, 1.00), v1=(2.00, 1.00, 2.00), v2=(1.00, 1.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 4: v0=(1.00, 1.00, 2.00), v1=(1.00, 2.00, 2.00), v2=(2.00, 2.00, 2.00), calculated normal=(0.00, 0.00, -1.00), magnitude=1
Triangle 5: v0=(1.00, 1.00, 2.00), v1=(2.00, 2.00, 2.00), v2=(2.00, 1.00, 2.00), calculated normal=(0.00, 0.00, -1.00), magnitude=1
Triangle 6: v0=(2.00, 1.00, 1.00), v1=(2.00, 2.00, 1.00), v2=(1.00, 2.00, 1.00), calculated normal=(0.00, 0.00, 1.00), magnitude=1
Triangle 7: v0=(2.00, 1.00, 1.00), v1=(1.00, 2.00, 1.00), v2=(1.00, 1.00, 1.00), calculated normal=(0.00, 0.00, 1.00), magnitude=1
Triangle 8: v0=(2.00, 1.00, 2.00), v1=(2.00, 2.00, 2.00), v2=(2.00, 2.00, 1.00), calculated normal=(-1.00, 0.00, 0.00), magnitude=1
Triangle 9: v0=(2.00, 1.00, 2.00), v1=(2.00, 2.00, 1.00), v2=(2.00, 1.00, 1.00), calculated normal=(-1.00, 0.00, 0.00), magnitude=1
Triangle 10: v0=(1.00, 1.00, 1.00), v1=(1.00, 2.00, 1.00), v2=(1.00, 2.00, 2.00), calculated normal=(1.00, 0.00, 0.00), magnitude=1
Triangle 11: v0=(1.00, 1.00, 1.00), v1=(1.00, 2.00, 2.00), v2=(1.00, 1.00, 2.00), calculated normal=(1.00, 0.00, 0.00), magnitude=1
Mesh validation - Vertices: 24, Triangles: 12, Normals: 24
Found 2 triangles with potentially incorrect winding order
Mesh bounds: Center: (1.50, 1.50, 1.50), Extents: (0.50, 0.50, 0.50)
Validation complete - Degenerate: 0, Wrong winding: 2
Generated mesh with 24 vertices and 12 triangles
]]></output>
          </test-case>
          <test-case id="1027" name="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" fullname="ExcidiumTests.VoxelChunkTests.GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" methodname="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="204116968" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000433" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1036" name="MeshBounds_ShouldMatchChunkSize" fullname="ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize" methodname="MeshBounds_ShouldMatchChunkSize" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="285177717" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000710" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1030" name="MeshGeneration_WhenCalled_ShouldCreateValidMesh" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WhenCalled_ShouldCreateValidMesh" methodname="MeshGeneration_WhenCalled_ShouldCreateValidMesh" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1061353653" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000440" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1031" name="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" methodname="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="282284140" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000343" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1035" name="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" fullname="ExcidiumTests.VoxelChunkTests.RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" methodname="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1660791750" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000335" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1029" name="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithInvalidCoordinates_ShouldNotCrash" methodname="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="466053293" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000730" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1028" name="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" methodname="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1797632475" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000264" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1033" name="TopFace_ShouldPointUpward" fullname="ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward" methodname="TopFace_ShouldPointUpward" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1790336655" result="Failed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.003830" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face normal should point upward. Normal: (0.00, 0.00, 0.00)
  Expected: greater than 0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward () [0x000eb] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:292
]]></stack-trace>
            </failure>
            <output><![CDATA[Mesh has zero-magnitude normals after RecalculateNormals()
Triangle 0: v0=(1.00, 1.00, 1.00), v1=(1.00, 1.00, 2.00), v2=(2.00, 1.00, 1.00), calculated normal=(0.00, 1.00, 0.00), magnitude=1
Triangle 1: v0=(1.00, 1.00, 1.00), v1=(2.00, 1.00, 2.00), v2=(1.00, 1.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 2: v0=(1.00, 0.00, 1.00), v1=(2.00, 0.00, 1.00), v2=(2.00, 0.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 3: v0=(1.00, 0.00, 1.00), v1=(2.00, 0.00, 2.00), v2=(1.00, 0.00, 2.00), calculated normal=(0.00, -1.00, 0.00), magnitude=1
Triangle 4: v0=(1.00, 0.00, 2.00), v1=(1.00, 1.00, 2.00), v2=(2.00, 1.00, 2.00), calculated normal=(0.00, 0.00, -1.00), magnitude=1
Triangle 5: v0=(1.00, 0.00, 2.00), v1=(2.00, 1.00, 2.00), v2=(2.00, 0.00, 2.00), calculated normal=(0.00, 0.00, -1.00), magnitude=1
Triangle 6: v0=(2.00, 0.00, 1.00), v1=(2.00, 1.00, 1.00), v2=(1.00, 1.00, 1.00), calculated normal=(0.00, 0.00, 1.00), magnitude=1
Triangle 7: v0=(2.00, 0.00, 1.00), v1=(1.00, 1.00, 1.00), v2=(1.00, 0.00, 1.00), calculated normal=(0.00, 0.00, 1.00), magnitude=1
Triangle 8: v0=(2.00, 0.00, 2.00), v1=(2.00, 1.00, 2.00), v2=(2.00, 1.00, 1.00), calculated normal=(-1.00, 0.00, 0.00), magnitude=1
Triangle 9: v0=(2.00, 0.00, 2.00), v1=(2.00, 1.00, 1.00), v2=(2.00, 0.00, 1.00), calculated normal=(-1.00, 0.00, 0.00), magnitude=1
Triangle 10: v0=(1.00, 0.00, 1.00), v1=(1.00, 1.00, 1.00), v2=(1.00, 1.00, 2.00), calculated normal=(1.00, 0.00, 0.00), magnitude=1
Triangle 11: v0=(1.00, 0.00, 1.00), v1=(1.00, 1.00, 2.00), v2=(1.00, 0.00, 2.00), calculated normal=(1.00, 0.00, 0.00), magnitude=1
Mesh validation - Vertices: 24, Triangles: 12, Normals: 24
Found 2 triangles with potentially incorrect winding order
Mesh bounds: Center: (1.50, 0.50, 1.50), Extents: (0.50, 0.50, 0.50)
Validation complete - Degenerate: 0, Wrong winding: 2
Generated mesh with 24 vertices and 12 triangles
]]></output>
          </test-case>
          <test-case id="1037" name="VoxelChunk_ShouldNotHaveMemoryLeaks" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks" methodname="VoxelChunk_ShouldNotHaveMemoryLeaks" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="922640752" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.176923" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1025" name="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" methodname="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1170957399" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000704" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1026" name="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" methodname="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1164830649" result="Passed" start-time="2025-07-06 19:39:09Z" end-time="2025-07-06 19:39:09Z" duration="0.000399" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>