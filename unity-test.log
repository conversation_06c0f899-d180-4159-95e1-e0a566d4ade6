[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Channel LicenseClient-patryk doesn't exist
[Licensing::Module] Successfully launched the LicensingClient (PId: 42748)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-patryk" at "2025-07-03T07:51:13.3951507Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 42748, path: "F:/Unity/Editor/6000.1.3f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.16.2+bdef8f5
  Session Id:              7277a4bf17644d6ca4b49cfbf25d6362
  Correlation Id:          bde873ea4b6d8c677f9e8df0e2c5c960
  External correlation Id: 2245704658290765859
  Machine Id:              3qpX9RBOm6ArecyxpFGyb6KeW74=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-patryk" (connect: 0.37s, validation: 0.04s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-patryk-notifications" at "2025-07-03T07:51:13.4551556Z"
[Licensing::Module] Connected to LicensingClient (PId: 42748, launch time: 0.00, total connection time: 0.43s)
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "9070991756955-UnityPersXXXX"
[Licensing::Client] Successfully updated license, isAync: True, time: 0.02
Built from '6000.1/staging' branch; Version is '6000.1.3f1 (f34db9734971) revision 15945145'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'de' Physical Memory: 63080 MB
[Licensing::Client] Successfully resolved entitlement details
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0
Date: 2025-07-03T07:51:13Z

COMMAND LINE ARGUMENTS:
F:\Unity\Editor\6000.1.3f1\Editor\Unity.exe
-runTests
-batchmode
-testResults
F:\Unity\Projects\Excidium\test-results-latest.xml
-testPlatform
EditMode
-logFile
F:\Unity\Projects\Excidium\unity-test.log
F:/Unity/Projects/Excidium
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [6684]  Target information:

Player connection [6684]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 2230284218 [EditorId] 2230284218 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6684]  * "[IP] ************* [Port] 55504 [Flags] 2 [Guid] 2230284218 [EditorId] 2230284218 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6684]  * "[IP] *********** [Port] 55504 [Flags] 2 [Guid] 2230284218 [EditorId] 2230284218 [Version] 1048832 [Id] WindowsEditor(7,Yggdrasil) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6684] Host joined multi-casting on [***********:54997]...
Player connection [6684] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Package Manager] Connected to IPC stream "Upm-42724" after 0.2 seconds.
Library Redirect Path: Library/
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 58 packages:
  Packages from [https://packages.unity.com]:
    com.unity.ai.navigation@2.0.7 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ai.navigation@39ae74efb85f)
    com.unity.burst@1.8.21 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.burst@59eb6f11d242)
    com.unity.cinemachine@2.10.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.cinemachine@dcc61ebd6655)
    com.unity.collab-proxy@2.8.2 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f)
    com.unity.collections@2.5.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.collections@56bff8827a7e)
    com.unity.ide.rider@3.0.36 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db)
    com.unity.ide.visualstudio@2.0.23 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13)
    com.unity.inputsystem@1.14.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.inputsystem@7fe8299111a7)
    com.unity.mathematics@1.3.2 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.mathematics@8017b507cc74)
    com.unity.nuget.mono-cecil@1.11.4 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f)
    com.unity.package-validation-suite@0.22.0-preview (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.package-validation-suite@536239bd7458)
    com.unity.test-framework.performance@3.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed)
    com.unity.timeline@1.8.7 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.timeline@c58b4ee65782)
    com.unity.visualscripting@1.9.6 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.visualscripting@7dcdc439b230)
    com.unity.searcher@4.9.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.searcher@1e17ce91558d)
  Built-in packages:
    com.unity.ext.nunit@2.0.5 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ext.nunit@031a54704bff)
    com.unity.multiplayer.center@1.0.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546)
    com.unity.render-pipelines.core@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.core@79d3abb9adf1)
    com.unity.render-pipelines.universal@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.universal@35104b1b69f5)
    com.unity.render-pipelines.universal-config@17.0.3 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.render-pipelines.universal-config@6af1487fecff)
    com.unity.rendering.light-transport@1.0.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.rendering.light-transport@ec31b4120e30)
    com.unity.shadergraph@17.1.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.shadergraph@26dc5ae27e7d)
    com.unity.test-framework@1.5.1 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.test-framework@4969648bc874)
    com.unity.ugui@2.0.0 (location: F:\Unity\Projects\Excidium\Library\PackageCache\com.unity.ugui@5c9fd4989cdd)
    com.unity.modules.accessibility@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director)
    com.unity.modules.hierarchycore@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore)
    com.unity.modules.imageconversion@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture)
    com.unity.modules.subsystems@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems)
    com.unity.modules.terrain@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: F:\Unity\Editor\6000.1.3f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr)
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.07 seconds
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.3f1 (f34db9734971)
[Subsystems] Discovering subsystems at path F:/Unity/Editor/6000.1.3f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Unity/Projects/Excidium/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon RX 7900 XT (ID=0x744c)
    Vendor:          ATI
    VRAM:            20464 MB
    App VRAM Budget: 19649 MB
    Driver:          32.0.21013.1000
[Licensing::Client] Successfully resolved entitlement details
Initialize mono
Mono path[0] = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity/Editor/6000.1.3f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56724
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity/Editor/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: F:/Unity/Editor/6000.1.3f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002751 seconds.
- Loaded All Assemblies, in  0.255 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.180 seconds
Domain Reload Profiling: 434ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (95ms)
		LoadAssemblies (98ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (93ms)
			TypeCache.Refresh (92ms)
				TypeCache.ScanAssembly (84ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (180ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (160ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (30ms)
			ProcessInitializeOnLoadAttributes (68ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Starting: F:\Unity\Editor\6000.1.3f1\Editor\Data\bee_backend.exe --ipc --defer-dag-verification --dagfile="Library/Bee/1900b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: F:/Unity/Projects/Excidium
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 0s303ms
[666/850    0s] ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput
*** Tundra build success (0.28 seconds), 1 items updated, 850 evaluated
AssetDatabase: script compilation time: 0.590228s
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.609 seconds
Refreshing native plugins compatible for Editor in 0.44 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.937 seconds
Domain Reload Profiling: 2545ms
	BeginReloadAssembly (242ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (137ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (319ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (140ms)
			TypeCache.Refresh (105ms)
				TypeCache.ScanAssembly (97ms)
			BuildScriptInfoCaches (26ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1938ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1799ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (1652ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Asset Pipeline Refresh (id=42f19609e8e1c744b8034ad6c147b5c4): Total: 3.490 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2225 ms
		Asset DB Callback time: managed=15 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=609 ms, compile time=591 ms, other=46 ms
		Project Asset Count: scripts=1323, non-scripts=2392
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 1.056ms
	ApplyChangesToAssetFolders: 0.347ms
	Scan: 83.639ms
	OnSourceAssetsModified: 0.002ms
	CategorizeAssetsWithTransientArtifact: 28.756ms
	ProcessAssetsWithTransientArtifactChanges: 45.503ms
	CategorizeAssets: 34.202ms
	ImportOutOfDateAssets: 1944.199ms (1351.382ms without children)
		CompileScripts: 591.328ms
		ReloadNativeAssets: 0.016ms
		UnloadImportedAssets: 0.836ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.369ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.001ms
		OnDemandSchedulerStart: 0.266ms
	PostProcessAllAssets: 15.598ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.001ms
	UnloadStreamsBegin: 1.449ms
	PersistCurrentRevisions: 0.067ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 7.433ms
	Untracked: 1334.884ms

Application.AssetDatabase Initial Refresh End
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Scanning for USB devices : 34.922ms
Initializing Unity extensions:
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Excidium
Unloading 16 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6256 unused Assets / (4.8 MB). Loaded Objects now: 7019.
Memory consumption went from 210.2 MB to 205.4 MB.
Total: 7.573800 ms (FindLiveObjects: 0.624600 ms CreateObjectMapping: 0.143000 ms MarkObjects: 5.005600 ms  DeleteObjects: 1.799800 ms)

D3D11 device created for Microsoft Media Foundation video decoding.
[Project] Loading completed in 5.878 seconds
	Project init time: 				5.025 seconds
		Template init time: 		0.000 seconds
		Package Manager init time: 		0.210 seconds
		Asset Database init time: 		0.122 seconds
		Global illumination init time: 	0.002 seconds
		Assemblies load time: 			0.446 seconds
		Unity extensions init time: 	0.021 seconds
		Asset Database refresh time: 	3.490 seconds
	Scene opening time: 			0.300 seconds
##utp:{"type":"ProjectInfo","version":2,"phase":"Immediate","time":1751529078890,"processId":42724,"projectLoad":5.8781371,"projectInit":5.0249958,"templateInit":0.0,"packageManagerInit":0.2101202,"assetDatabaseInit":0.1221592,"globalIlluminationInit":0.0021227,"assembliesLoad":0.4462775,"unityExtensionsInit":0.021225,"assetDatabaseRefresh":3.4898218,"sceneOpening":0.2995588}
##utp:{"type":"EditorInfo","version":2,"phase":"Immediate","time":1751529078890,"processId":42724,"editorVersion":"6000.1.3f1 (f34db9734971)","branch":"6000.1/staging","buildType":"Release","platform":"Windows"}
Asset Pipeline Refresh (id=168c74ce0dd449f42856440c918065cb): Total: 0.030 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
Running tests for ExecutionSettings with details:
targetPlatform = 
playerHeartbeatTimeout = 600
filters[0] = 
   Filter with settings:
   testMode = EditMode
   targetPlatform = 
   testNames = null
   groupNames = null
   categoryNames = null
   assemblyNames = null
ignoreTests = {}
featureFlags = null

UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:InitializeAndExecuteRun (string[]) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:53)
UnityEditor.TestTools.TestRunner.CommandLineTest.TestStarter:InitializeAndExecuteRun () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/TestStarter.cs:64)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs Line: 53)

Couldn't find a readme
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ReadmeEditor:SelectReadme () (at Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs:90)
ReadmeEditor:SelectReadmeAutomatically () (at Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs:58)
UnityEditor.EditorApplication:Internal_CallDelayFunctions ()

(Filename: Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs Line: 90)

[UnityConnectServicesConfig] config is NOT valid, switching to default
Created GICache directory at C:/Users/<USER>/AppData/LocalLow/Unity/Caches/GiCache. Took: 0.013s, timestamps: [5.886 - 5.899]
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1 unused Assets / (0.5 KB). Loaded Objects now: 7064.
Memory consumption went from 211.8 MB to 211.8 MB.
Total: 30.293000 ms (FindLiveObjects: 0.227900 ms CreateObjectMapping: 0.066400 ms MarkObjects: 29.981100 ms  DeleteObjects: 0.016800 ms)

Executing IPrebuildSetup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<ExecuteMethods>d__8<UnityEngine.TestTools.IPrebuildSetup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:51)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<UnityEngine.TestTools.IPrebuildSetup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:35)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs Line: 51)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<GenerationTime_ShouldScaleReasonablyWithSize>d__10:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:233)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Large chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<LargeChunk_ShouldGenerateWithinReasonableTime>d__6:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:90)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 90)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Medium chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<MediumChunk_ShouldGenerateWithinBudget>d__5:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:72)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 72)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Memory used for chunk generation: 0,00MB
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<MeshGeneration_ShouldNotExcessivelyAllocateMemory>d__7:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:124)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 124)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Small chunk generation time: 0ms
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
ExcidiumTests.VoxelChunkPerformanceTests/<SmallChunk_ShouldGenerateQuickly>d__4:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:54)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs Line: 54)

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:337)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:340)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:337)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:340)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:337)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:340)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:<>m__Finally1 () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:337)
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:System.IDisposable.Dispose ()
ExcidiumTests.VoxelChunkPerformanceTests/<StressTest_MultipleChunksSimultaneously>d__12:MoveNext () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:340)
UnityEngine.TestTools.TestEnumerator/<Execute>d__7:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:78)
UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:64)
UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableTestMethodCommand.cs:47)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:48)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.Object:Destroy (UnityEngine.Object,single)
UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEngine.TestTools.SetUpTearDownCommand/<InvokeAfter>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/SetUpTearDownCommand.cs:84)
UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Attributes/EnumeratorHelper.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:248)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs:100)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces () (at Assets/ExcidiumTests/VoxelChunkTests.cs:313)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:FaceGeneration_ShouldHaveCorrectWindingOrder () (at Assets/ExcidiumTests/VoxelChunkTests.cs:215)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshBounds_ShouldMatchChunkSize () (at Assets/ExcidiumTests/VoxelChunkTests.cs:355)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshGeneration_WhenCalled_ShouldCreateValidMesh () (at Assets/ExcidiumTests/VoxelChunkTests.cs:148)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount () (at Assets/ExcidiumTests/VoxelChunkTests.cs:181)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:RegenerateChunk_ShouldProduceSameResultAsInitialGeneration () (at Assets/ExcidiumTests/VoxelChunkTests.cs:335)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:RegenerateChunk_ShouldProduceSameResultAsInitialGeneration () (at Assets/ExcidiumTests/VoxelChunkTests.cs:341)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:TopFace_ShouldPointUpward () (at Assets/ExcidiumTests/VoxelChunkTests.cs:261)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEngine.TestTools.UnityTestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/UnityTestMethodCommand.cs:16)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:CaptureException (NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:72)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:83)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:24)
UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/UnityLogCheckDelegatingCommand.cs:35)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<NUnit.Framework.ITestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/ImmediateEnumerableCommand.cs:18)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:219)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<System.Reflection.MethodInfo>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<UnityEngine.TestTools.IOuterUnityTestAction>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/BeforeAfterTestCommandBase.cs:212)
UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RetryCommand.cs:37)
UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/RepeatCommand.cs:36)
UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/TimeoutCommand.cs:33)
UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/IgnoreTestCommand.cs:42)
UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Commands/StrictCheckCommand.cs:29)
UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/DefaultTestWorkItem.cs:50)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:207)
UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEngine.TestRunner/NUnitExtensions/Runner/CompositeWorkItem.cs:82)
UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:167)
UnityEditor.TestTools.TestRunner.EditModeRunner:TestConsumer (UnityEditor.TestTools.TestRunner.TestRunnerStateSerializer) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs:200)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.EditModeRunTask/<Execute>d__1:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/EditModeRunTask.cs:48)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

[./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs line 167]

Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
Saving results to: F:\Unity\Projects\Excidium\test-results-latest.xml
Executing IPostBuildCleanup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<ExecuteMethods>d__8<UnityEngine.TestTools.IPostBuildCleanup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:51)
UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<UnityEngine.TestTools.IPostBuildCleanup>:MoveNext () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs:35)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:173)
UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/TestJobRunner.cs:108)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs Line: 51)

Asset Pipeline Refresh (id=c6cebf5cf2ca3634894eb5855d3593ed): Total: 0.003 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 7065.
Memory consumption went from 202.9 MB to 202.9 MB.
Total: 5.896500 ms (FindLiveObjects: 0.192300 ms CreateObjectMapping: 0.077800 ms MarkObjects: 5.620500 ms  DeleteObjects: 0.005100 ms)

DisplayProgressbar: Undo
Test run completed. Exiting with code 2 (Failed). One or more tests failed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:ExitApplication (UnityEditor.TestTools.TestRunner.CommandLineTest.Executer/ReturnCodes,string) (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:81)
UnityEditor.TestTools.TestRunner.CommandLineTest.Executer:ExitIfRunIsCompleted () (at ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs:74)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.test-framework@4969648bc874/UnityEditor.TestRunner/CommandLineTest/Executer.cs Line: 81)

[Physics::Module] Cleanup current backned.
[Physics::Module] Id: 0xf2b8ea05
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-patryk-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-patryk channel disconnected successfully.
AcceleratorClientConnectionCallback - disconnected - :0
Cleanup mono
09:51:20.585 |V| RiderPlugin                    | :1                             | AppDomain.CurrentDomain.DomainUnload lifetimeDefinition.Terminate
Checking for leaked weakptr:
  Found no leaked weakptrs.
[Package Manager] Server process was shutdown
##utp:{"type":"MemoryLeaks","version":2,"phase":"Immediate","time":1751529080904,"processId":42724,"allocatedMemory":14347246,"memoryLabels":[{"Default":14269},{"Permanent":15412},{"NewDelete":68265},{"Thread":4237960},{"Manager":30682},{"VertexData":8},{"Geometry":816},{"VFX":-104},{"Texture":296},{"Shader":115649},{"Material":24},{"GfxDevice":57248},{"Animation":328},{"Audio":4040},{"FontEngine":256},{"Physics":1241},{"Serialization":880},{"Input":14824},{"JobScheduler":56040},{"TextLib":160},{"Mono":32},{"ScriptingNativeRuntime":155060},{"BaseObject":1619952},{"Resource":1304},{"Renderer":2928},{"Transform":4646},{"File":1016},{"WebCam":616},{"Culling":32},{"Terrain":11394},{"Wind":24},{"STL":40},{"String":9419},{"DynamicArray":116703},{"HashMap":104015},{"Utility":2667110},{"Curl":48},{"PoolAlloc":2056},{"AI":40},{"TypeTree":5005},{"ScriptManager":512},{"RuntimeInitializeOnLoadManager":64},{"SpriteAtlas":112},{"GI":7824},{"Director":8240},{"WebRequest":808},{"VR":46058},{"SceneManager":496},{"Video":208},{"LazyScriptCache":32},{"NativeArray":8},{"Camera":17},{"Secure":1},{"SerializationCache":1576},{"APIUpdating":10320},{"Subsystems":336},{"VirtualTexturing":57888},{"StaticSafetyDebugInfo":278528},{"Analytics":256},{"Hierarchy":272},{"Gui":80},{"EditorUtility":95050},{"VersionControl":4},{"Undo":947},{"AssetDatabase":4473442},{"EditorGi":328},{"UnityConnect":24536},{"Upm":1972},{"DrivenProperties":72},{"LocalIPC":262},{"ProfilerEditor":9738},{"CoreBusinessMetrics":3949},{"Licensing":3512},{"AssetReference":32},{"IPCStream":32}]}
