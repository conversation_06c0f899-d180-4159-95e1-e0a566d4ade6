<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="6" result="Failed(Child)" total="6" passed="0" failed="6" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0,0324071">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="6" result="Failed" site="Child" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.032407" total="6" passed="0" failed="6" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1008" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="6" result="Failed" site="Child" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.028281" total="6" passed="0" failed="6" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="42520" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1009" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="6" result="Failed" site="Child" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.028095" total="6" passed="0" failed="6" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelFaceGenerationTests" fullname="ExcidiumTests.VoxelFaceGenerationTests" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" testcasecount="6" result="Failed" site="Child" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.027582" total="6" passed="0" failed="6" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1007" name="AllFaces_ShouldPointOutward_ComprehensiveTest" fullname="ExcidiumTests.VoxelFaceGenerationTests.AllFaces_ShouldPointOutward_ComprehensiveTest" methodname="AllFaces_ShouldPointOutward_ComprehensiveTest" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="42842366" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.016560" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face should have 4 vertices
  Expected: 4
  But was:  12
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.AllFaces_ShouldPointOutward_ComprehensiveTest () [0x00378] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:304
]]></stack-trace>
            </failure>
            <output><![CDATA[Face normal analysis for voxel at (1,1,1):
Top face vertex 0: (1.00, 2.00, 1.00), normal: (0.00, 1.00, 0.00), Y-component: 1
Top face vertex 1: (1.00, 2.00, 2.00), normal: (0.00, 1.00, 0.00), Y-component: 1
Top face vertex 2: (2.00, 2.00, 1.00), normal: (0.00, 1.00, 0.00), Y-component: 1
Top face vertex 3: (2.00, 2.00, 2.00), normal: (0.00, 1.00, 0.00), Y-component: 1
Bottom face vertex 4: (1.00, 1.00, 1.00), normal: (0.00, -1.00, 0.00), Y-component: -1
Bottom face vertex 5: (2.00, 1.00, 1.00), normal: (0.00, -1.00, 0.00), Y-component: -1
Bottom face vertex 6: (2.00, 1.00, 2.00), normal: (0.00, -1.00, 0.00), Y-component: -1
Bottom face vertex 7: (1.00, 1.00, 2.00), normal: (0.00, -1.00, 0.00), Y-component: -1
Bottom face vertex 8: (1.00, 1.00, 2.00), normal: (0.00, 0.00, -1.00), Y-component: 0
Top face vertex 9: (1.00, 2.00, 2.00), normal: (0.00, 0.00, -1.00), Y-component: 0
Top face vertex 10: (2.00, 2.00, 2.00), normal: (0.00, 0.00, -1.00), Y-component: 0
Bottom face vertex 11: (2.00, 1.00, 2.00), normal: (0.00, 0.00, -1.00), Y-component: 0
Bottom face vertex 12: (2.00, 1.00, 1.00), normal: (0.00, 0.00, 1.00), Y-component: 0
Top face vertex 13: (2.00, 2.00, 1.00), normal: (0.00, 0.00, 1.00), Y-component: 0
Top face vertex 14: (1.00, 2.00, 1.00), normal: (0.00, 0.00, 1.00), Y-component: 0
Bottom face vertex 15: (1.00, 1.00, 1.00), normal: (0.00, 0.00, 1.00), Y-component: 0
Bottom face vertex 16: (2.00, 1.00, 2.00), normal: (-1.00, 0.00, 0.00), Y-component: 0
Top face vertex 17: (2.00, 2.00, 2.00), normal: (-1.00, 0.00, 0.00), Y-component: 0
Top face vertex 18: (2.00, 2.00, 1.00), normal: (-1.00, 0.00, 0.00), Y-component: 0
Bottom face vertex 19: (2.00, 1.00, 1.00), normal: (-1.00, 0.00, 0.00), Y-component: 0
Bottom face vertex 20: (1.00, 1.00, 1.00), normal: (1.00, 0.00, 0.00), Y-component: 0
Top face vertex 21: (1.00, 2.00, 1.00), normal: (1.00, 0.00, 0.00), Y-component: 0
Top face vertex 22: (1.00, 2.00, 2.00), normal: (1.00, 0.00, 0.00), Y-component: 0
Bottom face vertex 23: (1.00, 1.00, 2.00), normal: (1.00, 0.00, 0.00), Y-component: 0
Face counts - Top: 12, Bottom: 12, Front: 0, Back: 0, Right: 0, Left: 0

]]></output>
          </test-case>
          <test-case id="1004" name="BackFace_ShouldPointOutward" fullname="ExcidiumTests.VoxelFaceGenerationTests.BackFace_ShouldPointOutward" methodname="BackFace_ShouldPointOutward" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="1790017365" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.001513" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Back face normal should point backward (-Z). Vertex: (1.00, 2.00, 1.00), Normal: (0.00, 1.00, 0.00)
  Expected: less than -0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.BackFace_ShouldPointOutward () [0x00079] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:150
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1003" name="FrontFace_ShouldPointOutward" fullname="ExcidiumTests.VoxelFaceGenerationTests.FrontFace_ShouldPointOutward" methodname="FrontFace_ShouldPointOutward" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="1594505794" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.000614" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Front face normal should point forward (+Z). Vertex: (1.00, 2.00, 2.00), Normal: (0.00, 1.00, 0.00)
  Expected: greater than 0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.FrontFace_ShouldPointOutward () [0x00079] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:112
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1006" name="LeftFace_ShouldPointOutward" fullname="ExcidiumTests.VoxelFaceGenerationTests.LeftFace_ShouldPointOutward" methodname="LeftFace_ShouldPointOutward" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="154871240" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.000498" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Left face normal should point left (-X). Vertex: (1.00, 2.00, 1.00), Normal: (0.00, 1.00, 0.00)
  Expected: less than -0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.LeftFace_ShouldPointOutward () [0x00079] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:226
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1005" name="RightFace_ShouldPointOutward" fullname="ExcidiumTests.VoxelFaceGenerationTests.RightFace_ShouldPointOutward" methodname="RightFace_ShouldPointOutward" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="1111563633" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.000530" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Right face normal should point right (+X). Vertex: (2.00, 2.00, 1.00), Normal: (0.00, 1.00, 0.00)
  Expected: greater than 0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.RightFace_ShouldPointOutward () [0x00079] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:188
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1002" name="TopAndBottomFaces_ShouldPointOutward" fullname="ExcidiumTests.VoxelFaceGenerationTests.TopAndBottomFaces_ShouldPointOutward" methodname="TopAndBottomFaces_ShouldPointOutward" classname="ExcidiumTests.VoxelFaceGenerationTests" runstate="Runnable" seed="242387526" result="Failed" start-time="2025-07-06 20:35:02Z" end-time="2025-07-06 20:35:02Z" duration="0.000690" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Bottom face normal should point downward. Vertex: (1.00, 1.00, 2.00), Normal: (0.00, 0.00, -1.00)
  Expected: less than -0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelFaceGenerationTests.TopAndBottomFaces_ShouldPointOutward () [0x000e2] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelFaceGenerationTests.cs:73
]]></stack-trace>
            </failure>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>