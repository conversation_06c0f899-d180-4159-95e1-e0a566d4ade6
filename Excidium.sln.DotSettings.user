<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ABehaviour_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89527d299cb64ba88f87240781b415e51ea400_003F8a_003Fa93ac831_003FBehaviour_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AComponent_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89527d299cb64ba88f87240781b415e51ea400_003F24_003Faa032c08_003FComponent_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ADebug_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89527d299cb64ba88f87240781b415e51ea400_003F7e_003Fe72b1976_003FDebug_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003APhysics_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F6ce09342f8034704bcdf35e10f5a63d82b000_003Fb2_003F0f4ea257_003FPhysics_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AProfiler_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89527d299cb64ba88f87240781b415e51ea400_003F35_003F76dec525_003FProfiler_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AResources_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FAppData_003FJetBrains_003Fconfig_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89527d299cb64ba88f87240781b415e51ea400_003F18_003F07aee4a4_003FResources_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=be0cf929_002D1a8b_002D4e0f_002Da86c_002Dbd5eb46a5d27/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="All tests from &amp;lt;ExcidiumTests&amp;gt;" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;Project Location="F:\Unity\Projects\Excidium" Presentation="&amp;lt;ExcidiumTests&amp;gt;" /&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	</wpf:ResourceDictionary>