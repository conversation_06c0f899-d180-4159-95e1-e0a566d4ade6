<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="34" result="Failed(Child)" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0,7981432">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0.798143" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1038" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0.793348" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="21244" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1039" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0.793145" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelChunkIntegrationTests" fullname="ExcidiumTests.VoxelChunkIntegrationTests" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" testcasecount="12" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.047305" total="12" passed="12" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-case id="1011" name="ChunkBoundaryVoxels_ShouldRenderCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.ChunkBoundaryVoxels_ShouldRenderCorrectly" methodname="ChunkBoundaryVoxels_ShouldRenderCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1630734515" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.014911" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1013" name="DebugValidation_WhenEnabled_ShouldNotThrowErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.DebugValidation_WhenEnabled_ShouldNotThrowErrors" methodname="DebugValidation_WhenEnabled_ShouldNotThrowErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="857968490" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002309" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1009" name="EmptyChunk_ShouldHandleGracefully" fullname="ExcidiumTests.VoxelChunkIntegrationTests.EmptyChunk_ShouldHandleGracefully" methodname="EmptyChunk_ShouldHandleGracefully" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1946438558" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.004345" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1010" name="FullChunk_ShouldOnlyRenderExteriorFaces" fullname="ExcidiumTests.VoxelChunkIntegrationTests.FullChunk_ShouldOnlyRenderExteriorFaces" methodname="FullChunk_ShouldOnlyRenderExteriorFaces" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="308329146" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001591" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1005" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkIntegrationTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="127212853" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001898" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1004" name="MeshCollider_ShouldBeUpdatedWithMesh" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshCollider_ShouldBeUpdatedWithMesh" methodname="MeshCollider_ShouldBeUpdatedWithMesh" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="363340445" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001207" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1008" name="MeshNormals_ShouldBeCalculatedCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshNormals_ShouldBeCalculatedCorrectly" methodname="MeshNormals_ShouldBeCalculatedCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="838382675" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002470" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1007" name="MeshRenderer_ShouldHaveMaterialAssigned" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshRenderer_ShouldHaveMaterialAssigned" methodname="MeshRenderer_ShouldHaveMaterialAssigned" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1218344186" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.004012" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1006" name="MultipleRegenerations_ShouldNotCausePerformanceDegradation" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MultipleRegenerations_ShouldNotCausePerformanceDegradation" methodname="MultipleRegenerations_ShouldNotCausePerformanceDegradation" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="2003148270" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002693" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1012" name="RapidModifications_ShouldNotCauseErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RapidModifications_ShouldNotCauseErrors" methodname="RapidModifications_ShouldNotCauseErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="50372835" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002141" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1003" name="RuntimeModification_ShouldUpdateMeshCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RuntimeModification_ShouldUpdateMeshCorrectly" methodname="RuntimeModification_ShouldUpdateMeshCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="412959920" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001140" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1002" name="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" fullname="ExcidiumTests.VoxelChunkIntegrationTests.VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" methodname="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1374538037" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000763" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1014" name="VoxelChunkPerformanceTests" fullname="ExcidiumTests.VoxelChunkPerformanceTests" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" testcasecount="9" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.556087" total="9" passed="9" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-case id="1020" name="FaceCulling_ShouldSignificantlyReduceTriangleCount" fullname="ExcidiumTests.VoxelChunkPerformanceTests.FaceCulling_ShouldSignificantlyReduceTriangleCount" methodname="FaceCulling_ShouldSignificantlyReduceTriangleCount" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="926820677" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.003301" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Face culling effectiveness - Isolated: 768 triangles, Connected: 192 triangles, Reduction: 75,0%
]]></output>
          </test-case>
          <test-case id="1021" name="GenerationTime_ShouldScaleReasonablyWithSize" fullname="ExcidiumTests.VoxelChunkPerformanceTests.GenerationTime_ShouldScaleReasonablyWithSize" methodname="GenerationTime_ShouldScaleReasonablyWithSize" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1287997594" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.003119" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Chunk size 4�: 0ms
Chunk size 8�: 0ms
Chunk size 16�: 0ms
]]></output>
          </test-case>
          <test-case id="1017" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkPerformanceTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="50856532" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002323" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Large chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1016" name="MediumChunk_ShouldGenerateWithinBudget" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MediumChunk_ShouldGenerateWithinBudget" methodname="MediumChunk_ShouldGenerateWithinBudget" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1351366883" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001902" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Medium chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1018" name="MeshGeneration_ShouldNotExcessivelyAllocateMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MeshGeneration_ShouldNotExcessivelyAllocateMemory" methodname="MeshGeneration_ShouldNotExcessivelyAllocateMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="268119892" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.181762" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory used for chunk generation: 0,00MB
]]></output>
          </test-case>
          <test-case id="1019" name="MultipleRegenerations_ShouldNotLeakMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MultipleRegenerations_ShouldNotLeakMemory" methodname="MultipleRegenerations_ShouldNotLeakMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1643658381" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.354806" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory difference after regenerations: -0,16MB
]]></output>
          </test-case>
          <test-case id="1022" name="PerformanceRegression_ShouldMaintainBaselinePerformance" fullname="ExcidiumTests.VoxelChunkPerformanceTests.PerformanceRegression_ShouldMaintainBaselinePerformance" methodname="PerformanceRegression_ShouldMaintainBaselinePerformance" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1724113818" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002451" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Performance baseline check - Time: 0ms, Triangles/Voxel: 0,6
]]></output>
          </test-case>
          <test-case id="1015" name="SmallChunk_ShouldGenerateQuickly" fullname="ExcidiumTests.VoxelChunkPerformanceTests.SmallChunk_ShouldGenerateQuickly" methodname="SmallChunk_ShouldGenerateQuickly" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1488879404" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001820" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Small chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1023" name="StressTest_MultipleChunksSimultaneously" fullname="ExcidiumTests.VoxelChunkPerformanceTests.StressTest_MultipleChunksSimultaneously" methodname="StressTest_MultipleChunksSimultaneously" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1218280699" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.003625" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Stress test completed - 4 chunks in 1ms
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1024" name="VoxelChunkTests" fullname="ExcidiumTests.VoxelChunkTests" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0.189049" total="13" passed="12" failed="1" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1034" name="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" fullname="ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" methodname="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2136407513" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.001487" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1032" name="FaceGeneration_ShouldHaveCorrectWindingOrder" fullname="ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder" methodname="FaceGeneration_ShouldHaveCorrectWindingOrder" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="357437170" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.002185" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Mesh validation - Vertices: 24, Triangles: 12, Normals: 24
Mesh bounds: Center: (1.50, 1.50, 1.50), Extents: (0.50, 0.50, 0.50)
Validation complete - Degenerate: 0, Wrong winding: 0
Generated mesh with 24 vertices and 12 triangles
]]></output>
          </test-case>
          <test-case id="1027" name="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" fullname="ExcidiumTests.VoxelChunkTests.GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" methodname="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1296470588" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000423" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1036" name="MeshBounds_ShouldMatchChunkSize" fullname="ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize" methodname="MeshBounds_ShouldMatchChunkSize" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="378602927" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000646" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1030" name="MeshGeneration_WhenCalled_ShouldCreateValidMesh" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WhenCalled_ShouldCreateValidMesh" methodname="MeshGeneration_WhenCalled_ShouldCreateValidMesh" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1239914917" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000426" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1031" name="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" methodname="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="340916929" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000323" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1035" name="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" fullname="ExcidiumTests.VoxelChunkTests.RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" methodname="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1561474165" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000334" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1029" name="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithInvalidCoordinates_ShouldNotCrash" methodname="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1452839132" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000729" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1028" name="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" methodname="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="421642837" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.000257" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1033" name="TopFace_ShouldPointUpward" fullname="ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward" methodname="TopFace_ShouldPointUpward" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1251030622" result="Failed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:03Z" duration="0.003334" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face normal should point upward. Normal: (0.00, -1.00, 0.00)
  Expected: greater than 0.800000012f
  But was:  -1.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward () [0x000eb] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:292
]]></stack-trace>
            </failure>
            <output><![CDATA[Mesh validation - Vertices: 24, Triangles: 12, Normals: 24
Mesh bounds: Center: (1.50, 0.50, 1.50), Extents: (0.50, 0.50, 0.50)
Validation complete - Degenerate: 0, Wrong winding: 0
Generated mesh with 24 vertices and 12 triangles
]]></output>
          </test-case>
          <test-case id="1037" name="VoxelChunk_ShouldNotHaveMemoryLeaks" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks" methodname="VoxelChunk_ShouldNotHaveMemoryLeaks" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1474791021" result="Passed" start-time="2025-07-06 19:40:03Z" end-time="2025-07-06 19:40:04Z" duration="0.176008" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1025" name="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" methodname="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1041150048" result="Passed" start-time="2025-07-06 19:40:04Z" end-time="2025-07-06 19:40:04Z" duration="0.000791" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1026" name="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" methodname="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2104714709" result="Passed" start-time="2025-07-06 19:40:04Z" end-time="2025-07-06 19:40:04Z" duration="0.000467" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>