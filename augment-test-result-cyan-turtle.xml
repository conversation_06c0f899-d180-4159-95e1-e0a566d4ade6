<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="34" result="Failed(Child)" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0,7506236">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0.750624" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1038" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0.747125" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="43920" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1039" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0.746961" total="34" passed="33" failed="1" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelChunkIntegrationTests" fullname="ExcidiumTests.VoxelChunkIntegrationTests" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" testcasecount="12" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.043103" total="12" passed="12" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-case id="1011" name="ChunkBoundaryVoxels_ShouldRenderCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.ChunkBoundaryVoxels_ShouldRenderCorrectly" methodname="ChunkBoundaryVoxels_ShouldRenderCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1060668489" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.014164" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1013" name="DebugValidation_WhenEnabled_ShouldNotThrowErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.DebugValidation_WhenEnabled_ShouldNotThrowErrors" methodname="DebugValidation_WhenEnabled_ShouldNotThrowErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="310616301" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001185" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1009" name="EmptyChunk_ShouldHandleGracefully" fullname="ExcidiumTests.VoxelChunkIntegrationTests.EmptyChunk_ShouldHandleGracefully" methodname="EmptyChunk_ShouldHandleGracefully" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="437817159" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.003771" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1010" name="FullChunk_ShouldOnlyRenderExteriorFaces" fullname="ExcidiumTests.VoxelChunkIntegrationTests.FullChunk_ShouldOnlyRenderExteriorFaces" methodname="FullChunk_ShouldOnlyRenderExteriorFaces" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="729200480" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001286" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1005" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkIntegrationTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="855198554" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001593" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1004" name="MeshCollider_ShouldBeUpdatedWithMesh" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshCollider_ShouldBeUpdatedWithMesh" methodname="MeshCollider_ShouldBeUpdatedWithMesh" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1386447992" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001140" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1008" name="MeshNormals_ShouldBeCalculatedCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshNormals_ShouldBeCalculatedCorrectly" methodname="MeshNormals_ShouldBeCalculatedCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="582000562" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.002454" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1007" name="MeshRenderer_ShouldHaveMaterialAssigned" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshRenderer_ShouldHaveMaterialAssigned" methodname="MeshRenderer_ShouldHaveMaterialAssigned" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="939403272" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.003746" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1006" name="MultipleRegenerations_ShouldNotCausePerformanceDegradation" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MultipleRegenerations_ShouldNotCausePerformanceDegradation" methodname="MultipleRegenerations_ShouldNotCausePerformanceDegradation" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1927435712" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.002714" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1012" name="RapidModifications_ShouldNotCauseErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RapidModifications_ShouldNotCauseErrors" methodname="RapidModifications_ShouldNotCauseErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="211373343" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.002195" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1003" name="RuntimeModification_ShouldUpdateMeshCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RuntimeModification_ShouldUpdateMeshCorrectly" methodname="RuntimeModification_ShouldUpdateMeshCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="785571164" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001165" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1002" name="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" fullname="ExcidiumTests.VoxelChunkIntegrationTests.VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" methodname="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="184897018" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.000766" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1014" name="VoxelChunkPerformanceTests" fullname="ExcidiumTests.VoxelChunkPerformanceTests" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" testcasecount="9" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0.524323" total="9" passed="9" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-case id="1020" name="FaceCulling_ShouldSignificantlyReduceTriangleCount" fullname="ExcidiumTests.VoxelChunkPerformanceTests.FaceCulling_ShouldSignificantlyReduceTriangleCount" methodname="FaceCulling_ShouldSignificantlyReduceTriangleCount" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1968449740" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.003320" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Face culling effectiveness - Isolated: 768 triangles, Connected: 192 triangles, Reduction: 75,0%
]]></output>
          </test-case>
          <test-case id="1021" name="GenerationTime_ShouldScaleReasonablyWithSize" fullname="ExcidiumTests.VoxelChunkPerformanceTests.GenerationTime_ShouldScaleReasonablyWithSize" methodname="GenerationTime_ShouldScaleReasonablyWithSize" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="13894760" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.003007" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Chunk size 4�: 0ms
Chunk size 8�: 0ms
Chunk size 16�: 0ms
]]></output>
          </test-case>
          <test-case id="1017" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkPerformanceTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="818811289" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.002219" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Large chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1016" name="MediumChunk_ShouldGenerateWithinBudget" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MediumChunk_ShouldGenerateWithinBudget" methodname="MediumChunk_ShouldGenerateWithinBudget" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1617158399" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:40Z" duration="0.001790" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Medium chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1018" name="MeshGeneration_ShouldNotExcessivelyAllocateMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MeshGeneration_ShouldNotExcessivelyAllocateMemory" methodname="MeshGeneration_ShouldNotExcessivelyAllocateMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="59260181" result="Passed" start-time="2025-07-06 19:23:40Z" end-time="2025-07-06 19:23:41Z" duration="0.169626" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory used for chunk generation: 0,00MB
]]></output>
          </test-case>
          <test-case id="1019" name="MultipleRegenerations_ShouldNotLeakMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MultipleRegenerations_ShouldNotLeakMemory" methodname="MultipleRegenerations_ShouldNotLeakMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1925564686" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.335485" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory difference after regenerations: -0,16MB
]]></output>
          </test-case>
          <test-case id="1022" name="PerformanceRegression_ShouldMaintainBaselinePerformance" fullname="ExcidiumTests.VoxelChunkPerformanceTests.PerformanceRegression_ShouldMaintainBaselinePerformance" methodname="PerformanceRegression_ShouldMaintainBaselinePerformance" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1181416311" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.002313" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Performance baseline check - Time: 0ms, Triangles/Voxel: 0,6
]]></output>
          </test-case>
          <test-case id="1015" name="SmallChunk_ShouldGenerateQuickly" fullname="ExcidiumTests.VoxelChunkPerformanceTests.SmallChunk_ShouldGenerateQuickly" methodname="SmallChunk_ShouldGenerateQuickly" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1862403558" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.001875" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Small chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1023" name="StressTest_MultipleChunksSimultaneously" fullname="ExcidiumTests.VoxelChunkPerformanceTests.StressTest_MultipleChunksSimultaneously" methodname="StressTest_MultipleChunksSimultaneously" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="251380989" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.003720" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Stress test completed - 4 chunks in 1ms
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1024" name="VoxelChunkTests" fullname="ExcidiumTests.VoxelChunkTests" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.178872" total="13" passed="12" failed="1" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1034" name="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" fullname="ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" methodname="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2079453523" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.001476" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1032" name="FaceGeneration_ShouldHaveCorrectWindingOrder" fullname="ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder" methodname="FaceGeneration_ShouldHaveCorrectWindingOrder" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="97043357" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000609" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1027" name="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" fullname="ExcidiumTests.VoxelChunkTests.GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" methodname="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1480950329" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000402" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1036" name="MeshBounds_ShouldMatchChunkSize" fullname="ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize" methodname="MeshBounds_ShouldMatchChunkSize" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="356349950" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000748" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1030" name="MeshGeneration_WhenCalled_ShouldCreateValidMesh" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WhenCalled_ShouldCreateValidMesh" methodname="MeshGeneration_WhenCalled_ShouldCreateValidMesh" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2047488543" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000417" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1031" name="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" methodname="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1819870376" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000327" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1035" name="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" fullname="ExcidiumTests.VoxelChunkTests.RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" methodname="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1107181625" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000328" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1029" name="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithInvalidCoordinates_ShouldNotCrash" methodname="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1646975558" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000756" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1028" name="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" methodname="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="321750377" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000256" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1033" name="TopFace_ShouldPointUpward" fullname="ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward" methodname="TopFace_ShouldPointUpward" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1388140446" result="Failed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.002487" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face normal should point upward. Normal: (0.00, -1.00, 0.00)
  Expected: greater than 0.800000012f
  But was:  -1.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward () [0x000af] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:278
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1037" name="VoxelChunk_ShouldNotHaveMemoryLeaks" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks" methodname="VoxelChunk_ShouldNotHaveMemoryLeaks" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1659702330" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.168476" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1025" name="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" methodname="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="455127919" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000567" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1026" name="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" methodname="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1998434505" result="Passed" start-time="2025-07-06 19:23:41Z" end-time="2025-07-06 19:23:41Z" duration="0.000410" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>