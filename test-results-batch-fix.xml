<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="34" result="Failed(Child)" total="34" passed="25" failed="9" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0,8589759">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0.858976" total="34" passed="25" failed="9" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1038" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0.855191" total="34" passed="25" failed="9" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="41352" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1039" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0.855013" total="34" passed="25" failed="9" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelChunkIntegrationTests" fullname="ExcidiumTests.VoxelChunkIntegrationTests" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" testcasecount="12" result="Failed" site="Child" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.057999" total="12" passed="10" failed="2" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1011" name="ChunkBoundaryVoxels_ShouldRenderCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.ChunkBoundaryVoxels_ShouldRenderCorrectly" methodname="ChunkBoundaryVoxels_ShouldRenderCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="565261034" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.016285" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1013" name="DebugValidation_WhenEnabled_ShouldNotThrowErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.DebugValidation_WhenEnabled_ShouldNotThrowErrors" methodname="DebugValidation_WhenEnabled_ShouldNotThrowErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1123565677" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.001752" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1009" name="EmptyChunk_ShouldHandleGracefully" fullname="ExcidiumTests.VoxelChunkIntegrationTests.EmptyChunk_ShouldHandleGracefully" methodname="EmptyChunk_ShouldHandleGracefully" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="50952098" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.004248" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1010" name="FullChunk_ShouldOnlyRenderExteriorFaces" fullname="ExcidiumTests.VoxelChunkIntegrationTests.FullChunk_ShouldOnlyRenderExteriorFaces" methodname="FullChunk_ShouldOnlyRenderExteriorFaces" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="169287329" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.002074" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1005" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkIntegrationTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1123299367" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003535" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1004" name="MeshCollider_ShouldBeUpdatedWithMesh" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshCollider_ShouldBeUpdatedWithMesh" methodname="MeshCollider_ShouldBeUpdatedWithMesh" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="2025023617" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.001692" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1008" name="MeshNormals_ShouldBeCalculatedCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshNormals_ShouldBeCalculatedCorrectly" methodname="MeshNormals_ShouldBeCalculatedCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1569889148" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003136" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1007" name="MeshRenderer_ShouldHaveMaterialAssigned" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshRenderer_ShouldHaveMaterialAssigned" methodname="MeshRenderer_ShouldHaveMaterialAssigned" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="651805982" result="Failed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.005635" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  MeshRenderer should have a material
  Expected: not null
  But was:  null
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkIntegrationTests+<MeshRenderer_ShouldHaveMaterialAssigned>d__10.MoveNext () [0x00067] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkIntegrationTests.cs:161
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in .\Library\PackageCache\com.unity.test-framework@4969648bc874\UnityEngine.TestRunner\NUnitExtensions\Attributes\TestEnumerator.cs:44
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1006" name="MultipleRegenerations_ShouldNotCausePerformanceDegradation" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MultipleRegenerations_ShouldNotCausePerformanceDegradation" methodname="MultipleRegenerations_ShouldNotCausePerformanceDegradation" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1613680084" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003542" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1012" name="RapidModifications_ShouldNotCauseErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RapidModifications_ShouldNotCauseErrors" methodname="RapidModifications_ShouldNotCauseErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="417371418" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003236" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1003" name="RuntimeModification_ShouldUpdateMeshCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RuntimeModification_ShouldUpdateMeshCorrectly" methodname="RuntimeModification_ShouldUpdateMeshCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1003880623" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.002650" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1002" name="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" fullname="ExcidiumTests.VoxelChunkIntegrationTests.VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" methodname="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="385378857" result="Failed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.001631" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Expected log did not appear: [Error] Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.]]></message>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1014" name="VoxelChunkPerformanceTests" fullname="ExcidiumTests.VoxelChunkPerformanceTests" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" testcasecount="9" result="Failed" site="Child" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0.596048" total="9" passed="7" failed="2" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1020" name="FaceCulling_ShouldSignificantlyReduceTriangleCount" fullname="ExcidiumTests.VoxelChunkPerformanceTests.FaceCulling_ShouldSignificantlyReduceTriangleCount" methodname="FaceCulling_ShouldSignificantlyReduceTriangleCount" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="157814871" result="Failed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.004005" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Face culling not effective enough. Isolated: 592, Connected: 592, Ratio: 1,00
  Expected: less than 0.5f
  But was:  1.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkPerformanceTests+<FaceCulling_ShouldSignificantlyReduceTriangleCount>d__9.MoveNext () [0x0015a] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkPerformanceTests.cs:215
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in .\Library\PackageCache\com.unity.test-framework@4969648bc874\UnityEngine.TestRunner\NUnitExtensions\Attributes\TestEnumerator.cs:44
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1021" name="GenerationTime_ShouldScaleReasonablyWithSize" fullname="ExcidiumTests.VoxelChunkPerformanceTests.GenerationTime_ShouldScaleReasonablyWithSize" methodname="GenerationTime_ShouldScaleReasonablyWithSize" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="37139699" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003299" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Chunk size 4�: 0ms
Chunk size 8�: 0ms
Chunk size 16�: 0ms
]]></output>
          </test-case>
          <test-case id="1017" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkPerformanceTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1914499505" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.002836" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Large chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1016" name="MediumChunk_ShouldGenerateWithinBudget" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MediumChunk_ShouldGenerateWithinBudget" methodname="MediumChunk_ShouldGenerateWithinBudget" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="2000502102" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.002419" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Medium chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1018" name="MeshGeneration_ShouldNotExcessivelyAllocateMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MeshGeneration_ShouldNotExcessivelyAllocateMemory" methodname="MeshGeneration_ShouldNotExcessivelyAllocateMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="283845747" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.190343" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory used for chunk generation: 0,00MB
]]></output>
          </test-case>
          <test-case id="1019" name="MultipleRegenerations_ShouldNotLeakMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MultipleRegenerations_ShouldNotLeakMemory" methodname="MultipleRegenerations_ShouldNotLeakMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="7845245" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.380973" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Memory difference after regenerations: 0,29MB
]]></output>
          </test-case>
          <test-case id="1022" name="PerformanceRegression_ShouldMaintainBaselinePerformance" fullname="ExcidiumTests.VoxelChunkPerformanceTests.PerformanceRegression_ShouldMaintainBaselinePerformance" methodname="PerformanceRegression_ShouldMaintainBaselinePerformance" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="962716092" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:34Z" duration="0.003377" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
Performance baseline check - Time: 0ms, Triangles/Voxel: 0,6
]]></output>
          </test-case>
          <test-case id="1015" name="SmallChunk_ShouldGenerateQuickly" fullname="ExcidiumTests.VoxelChunkPerformanceTests.SmallChunk_ShouldGenerateQuickly" methodname="SmallChunk_ShouldGenerateQuickly" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1743220423" result="Passed" start-time="2025-07-03 14:53:34Z" end-time="2025-07-03 14:53:35Z" duration="0.003074" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Small chunk generation time: 0ms
]]></output>
          </test-case>
          <test-case id="1023" name="StressTest_MultipleChunksSimultaneously" fullname="ExcidiumTests.VoxelChunkPerformanceTests.StressTest_MultipleChunksSimultaneously" methodname="StressTest_MultipleChunksSimultaneously" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1583629953" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.004472" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Expected log did not appear: [Error] Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.]]></message>
            </failure>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1024" name="VoxelChunkTests" fullname="ExcidiumTests.VoxelChunkTests" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.200252" total="13" passed="8" failed="5" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1034" name="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" fullname="ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" methodname="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1048625329" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.002394" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Adjacent voxels should have 40 vertices (10 faces), got 240
  Expected: 40
  But was:  240
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces () [0x00093] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:326
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1032" name="FaceGeneration_ShouldHaveCorrectWindingOrder" fullname="ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder" methodname="FaceGeneration_ShouldHaveCorrectWindingOrder" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1561874261" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.001227" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1027" name="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" fullname="ExcidiumTests.VoxelChunkTests.GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" methodname="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="208084057" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000444" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1036" name="MeshBounds_ShouldMatchChunkSize" fullname="ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize" methodname="MeshBounds_ShouldMatchChunkSize" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1049513985" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.001196" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Bounds should include chunk max Y
  Expected: greater than or equal to 4.0f
  But was:  2.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize () [0x000ac] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:372
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1030" name="MeshGeneration_WhenCalled_ShouldCreateValidMesh" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WhenCalled_ShouldCreateValidMesh" methodname="MeshGeneration_WhenCalled_ShouldCreateValidMesh" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="756486343" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000798" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1031" name="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" methodname="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1409193403" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000751" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Single voxel should have 24 vertices (6 faces � 4 vertices)
  Expected: 24
  But was:  240
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount () [0x00074] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:187
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1035" name="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" fullname="ExcidiumTests.VoxelChunkTests.RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" methodname="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1069632169" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.001057" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1029" name="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithInvalidCoordinates_ShouldNotCrash" methodname="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1872376687" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000860" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1028" name="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" methodname="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="685772529" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000262" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1033" name="TopFace_ShouldPointUpward" fullname="ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward" methodname="TopFace_ShouldPointUpward" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="280976559" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000961" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face normal should point upward. Normal: (0.00, 0.00, -1.00)
  Expected: greater than 0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward () [0x000ba] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:282
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1037" name="VoxelChunk_ShouldNotHaveMemoryLeaks" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks" methodname="VoxelChunk_ShouldNotHaveMemoryLeaks" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1529143252" result="Failed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.187734" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Should not leak meshes. Initial: 76, Final: 81, Difference: 5
  Expected: less than or equal to 2
  But was:  5
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks () [0x00056] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:400
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1025" name="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" methodname="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2006372766" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000597" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1026" name="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" methodname="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="2078695912" result="Passed" start-time="2025-07-03 14:53:35Z" end-time="2025-07-03 14:53:35Z" duration="0.000364" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>